using UnityEngine;
using System.Collections;
using System.Collections.Generic;

/// <summary>
/// Manages world items and their container in the scene.
/// Provides a central point for registering and tracking world items.
/// Includes falling item recovery system to prevent items from falling through the world.
/// </summary>
[DefaultExecutionOrder(-100)] // Ensure this runs before other scripts
public class WorldItemManager : MonoBehaviour
{
    [SerializeField] private Transform itemsContainer;
    public Transform ItemsContainer => itemsContainer;
    
    [Header("Falling Item Recovery")]
    [SerializeField] private float fallThreshold = -50f; // Y position below which items are considered "fallen through"
    [SerializeField] private Transform recoveryPosition; // Where to move fallen items
    [SerializeField] private Vector3 recoveryOffset = Vector3.zero; // Offset from recovery position
    [SerializeField] private float checkInterval = 5f; // How often to check for fallen items (seconds) - increased for better performance
    [SerializeField] private bool enableRecoverySystem = true; // Toggle the system on/off
    [SerializeField] private bool logRecoveries = true; // Whether to log item recoveries
    [SerializeField] private bool enableUnregisteredItemCheck = false; // Enable expensive scene-wide search (disable for better performance)
    
    [Header("Recovery Physics")]
    [SerializeField] private float recoveryVelocityDamping = 0.1f; // How much to reduce velocity when recovering
    [SerializeField] private float recoveryHeight = 2f; // Height above recovery position to place items
    [SerializeField] private float recoverySpread = 3f; // Random spread area for recovered items
    
    private PersistenceManager progressManager;
    private List<InvItemPickup> trackedItems = new List<InvItemPickup>();
    private Camera mainCamera;
    
    // Reusable collections to avoid garbage collection
    private List<InvItemPickup> itemsToRecover = new List<InvItemPickup>();
    private List<InvItemPickup> tempItemList = new List<InvItemPickup>();
    
    // Cached values for performance
    private Vector3 cachedRecoveryBasePosition;
    private float lastRecoveryPositionUpdate;
    private const float RECOVERY_POSITION_CACHE_TIME = 1f; // Cache recovery position for 1 second
    
    // Singleton instance
    public static WorldItemManager Instance { get; private set; }
    
    private void Awake()
    {
        // Simple singleton pattern
        if (Instance == null)
        {
            Instance = this;
            
            // Create container if needed
            if (itemsContainer == null)
            {
                GameObject container = new GameObject("World_Items_Container");
                container.transform.SetParent(transform);
                itemsContainer = container.transform;
            }
            
            // Set up recovery position if not set
            if (recoveryPosition == null)
            {
                GameObject recoveryObj = new GameObject("Item_Recovery_Position");
                recoveryObj.transform.SetParent(transform);
                recoveryObj.transform.position = new Vector3(0, 10, 0); // Default safe position
                recoveryPosition = recoveryObj.transform;
                
                // Add a visual indicator in the editor
                if (Application.isEditor)
                {
                    var gizmo = recoveryObj.AddComponent<RecoveryPositionGizmo>();
                }
            }
            
            Debug.Log("[WorldItemManager] Initialized with world items container and recovery system");
        }
        else if (Instance != this)
        {
            Debug.LogWarning("[WorldItemManager] Another instance already exists. Destroying this one.");
            Destroy(gameObject);
            return;
        }
    }
    
    private void Start()
    {
        // Find progression manager
        progressManager = PersistenceManager.Instance;
        
        if (progressManager != null)
        {
            // Register our container
            progressManager.SetWorldItemsContainer(itemsContainer);
            Debug.Log("[WorldItemManager] Registered container with PlayerProgressionManager");
        }
        else
        {
            Debug.LogWarning("[WorldItemManager] Could not find PlayerProgressionManager instance!");
        }
        
        // Get main camera for player position reference
        mainCamera = Camera.main;
        
        // Start the falling item recovery system
        if (enableRecoverySystem)
        {
            StartCoroutine(FallingItemRecoveryLoop());
            Debug.Log($"[WorldItemManager] Started falling item recovery system (threshold: {fallThreshold})");
        }
    }
    
    private void Update()
    {
        // Debug controls for testing the recovery system (only in editor for performance)
        if (Application.isEditor)
        {
            if (Input.GetKeyDown(KeyCode.F10))
            {
                ManualRecoveryCheck();
            }
            
            if (Input.GetKeyDown(KeyCode.F11))
            {
                LogRecoveryStats();
            }
        }
    }
    
    /// <summary>
    /// Add an item to the world items container and register it for tracking
    /// </summary>
    /// <param name="item">The item to parent to the container</param>
    public void AddItemToContainer(GameObject item)
    {
        if (item != null && itemsContainer != null)
        {
            item.transform.SetParent(itemsContainer);
            
            // Register for falling item tracking
            var itemPickup = item.GetComponent<InvItemPickup>();
            if (itemPickup != null)
            {
                RegisterItemForTracking(itemPickup);
            }
            
            Debug.Log($"[WorldItemManager] Added {item.name} to container");
        }
    }
    
    /// <summary>
    /// Register an item for falling detection
    /// </summary>
    public void RegisterItemForTracking(InvItemPickup item)
    {
        if (item != null && !trackedItems.Contains(item))
        {
            trackedItems.Add(item);
        }
    }
    
    /// <summary>
    /// Unregister an item from falling detection (when picked up or destroyed)
    /// </summary>
    public void UnregisterItemFromTracking(InvItemPickup item)
    {
        if (item != null && trackedItems.Contains(item))
        {
            trackedItems.Remove(item);
        }
    }
    
    /// <summary>
    /// The main recovery loop that checks for fallen items
    /// </summary>
    private IEnumerator FallingItemRecoveryLoop()
    {
        while (enableRecoverySystem)
        {
            yield return new WaitForSeconds(checkInterval);
            
            // Clean up null references efficiently
            CleanupNullReferences();
            
            // Check all tracked items (this is the main check)
            CheckForFallenItems();
            
            // Only check for unregistered items if enabled (expensive operation)
            if (enableUnregisteredItemCheck)
            {
                CheckForUnregisteredFallenItems();
            }
        }
    }
    
    /// <summary>
    /// Efficiently clean up null references without creating garbage
    /// </summary>
    private void CleanupNullReferences()
    {
        // Use a more efficient approach than RemoveAll to avoid allocations
        for (int i = trackedItems.Count - 1; i >= 0; i--)
        {
            if (trackedItems[i] == null)
            {
                trackedItems.RemoveAt(i);
            }
        }
    }
    
    /// <summary>
    /// Check tracked items for falling through the world (optimized version)
    /// </summary>
    private void CheckForFallenItems()
    {
        // Clear the reusable list instead of creating a new one
        itemsToRecover.Clear();
        
        // Check tracked items for falling
        foreach (var item in trackedItems)
        {
            if (item != null && item.transform.position.y < fallThreshold)
            {
                itemsToRecover.Add(item);
            }
        }
        
        // Recover all fallen items
        foreach (var item in itemsToRecover)
        {
            RecoverFallenItem(item);
        }
    }
    
    /// <summary>
    /// Check for any unregistered items that might have fallen (EXPENSIVE - use sparingly)
    /// </summary>
    private void CheckForUnregisteredFallenItems()
    {
        // WARNING: This is expensive! Only use if items are somehow not being tracked properly
        if (logRecoveries)
        {
            Debug.LogWarning("[WorldItemManager] Performing expensive scene-wide item search. Consider disabling 'enableUnregisteredItemCheck' for better performance.");
        }
        
        // Clear temp list and reuse it
        tempItemList.Clear();
        
        // Find all InvItemPickup components in the scene (expensive operation)
        var allItems = FindObjectsOfType<InvItemPickup>();
        
        // First pass: identify items that need recovery
        foreach (var item in allItems)
        {
            if (item != null && item.transform.position.y < fallThreshold)
            {
                tempItemList.Add(item);
            }
        }
        
        // Second pass: process items that need recovery
        foreach (var item in tempItemList)
        {
            // Register it for tracking if not already tracked
            if (!trackedItems.Contains(item))
            {
                RegisterItemForTracking(item);
            }
            
            // Recover the item
            RecoverFallenItem(item);
        }
        
        if (logRecoveries && tempItemList.Count > 0)
        {
            Debug.Log($"[WorldItemManager] Found {tempItemList.Count} unregistered fallen items via scene search");
        }
    }
    
    /// <summary>
    /// Recover a fallen item by moving it to a safe position
    /// </summary>
    private void RecoverFallenItem(InvItemPickup item)
    {
        if (item == null) return;
        
        // Calculate recovery position
        Vector3 targetPosition = GetRecoveryPosition();
        
        // Move the item
        item.transform.position = targetPosition;
        
        // Reset physics if the item has a Rigidbody
        var rb = item.GetComponent<Rigidbody>();
        if (rb != null)
        {
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
            
            // Apply slight damping to prevent immediate re-falling
            rb.linearDamping = Mathf.Max(rb.linearDamping, recoveryVelocityDamping);
        }
        
        // Make sure the item is active
        item.gameObject.SetActive(true);
        
        // Log the recovery
        if (logRecoveries)
        {
            Debug.Log($"[WorldItemManager] Recovered fallen item: {item.name} from Y={item.transform.position.y:F2} to {targetPosition}");
        }
        
        // Update the progression manager about the item's new position
        if (progressManager != null)
        {
            progressManager.OnItemDropped(item, false); // false = not player dropped, just repositioned
        }
    }
    
    /// <summary>
    /// Calculate a safe recovery position with some randomization (optimized with caching)
    /// </summary>
    private Vector3 GetRecoveryPosition()
    {
        // Cache the base position for performance
        if (Time.time - lastRecoveryPositionUpdate > RECOVERY_POSITION_CACHE_TIME)
        {
            UpdateCachedRecoveryPosition();
        }
        
        // Add some randomization to prevent items from stacking
        Vector3 randomOffset = new Vector3(
            Random.Range(-recoverySpread, recoverySpread),
            Random.Range(0f, recoveryHeight),
            Random.Range(-recoverySpread, recoverySpread)
        );
        
        return cachedRecoveryBasePosition + randomOffset;
    }
    
    /// <summary>
    /// Update the cached recovery base position
    /// </summary>
    private void UpdateCachedRecoveryPosition()
    {
        // If we have a designated recovery position, use it
        if (recoveryPosition != null)
        {
            cachedRecoveryBasePosition = recoveryPosition.position + recoveryOffset;
        }
        else
        {
            // Fallback: use player position or world origin
            if (mainCamera != null)
            {
                cachedRecoveryBasePosition = mainCamera.transform.position + Vector3.up * 5f;
            }
            else
            {
                cachedRecoveryBasePosition = Vector3.up * 10f; // Safe height above origin
            }
        }
        
        lastRecoveryPositionUpdate = Time.time;
    }
    
    /// <summary>
    /// Reset all world item data
    /// </summary>
    public void ResetWorldItems()
    {
        if (progressManager != null)
        {
            progressManager.ResetWorldItemData();
            Debug.Log("[WorldItemManager] Reset world item tracking");
        }
        
        // Clear our tracking list
        trackedItems.Clear();
    }
    
    /// <summary>
    /// Manually trigger a recovery check (useful for testing)
    /// </summary>
    public void ManualRecoveryCheck()
    {
        if (enableRecoverySystem)
        {
            CheckForFallenItems();
            if (enableUnregisteredItemCheck)
            {
                CheckForUnregisteredFallenItems();
            }
            Debug.Log("[WorldItemManager] Manual recovery check completed");
        }
    }
    
    /// <summary>
    /// Batch register multiple items for tracking (more efficient than individual calls)
    /// </summary>
    public void BatchRegisterItems(InvItemPickup[] items)
    {
        if (items == null || items.Length == 0) return;
        
        foreach (var item in items)
        {
            if (item != null && !trackedItems.Contains(item))
            {
                trackedItems.Add(item);
            }
        }
        
        if (logRecoveries)
        {
            Debug.Log($"[WorldItemManager] Batch registered {items.Length} items for tracking");
        }
    }
    
    /// <summary>
    /// Set a custom recovery position
    /// </summary>
    public void SetRecoveryPosition(Transform position)
    {
        recoveryPosition = position;
        Debug.Log($"[WorldItemManager] Recovery position set to: {position.position}");
    }
    
    /// <summary>
    /// Get statistics about the recovery system
    /// </summary>
    public void LogRecoveryStats()
    {
        Debug.Log($"[WorldItemManager] Recovery Stats - Tracked Items: {trackedItems.Count}, Threshold: {fallThreshold}, Interval: {checkInterval}s");
    }
    
    /// <summary>
    /// Reposition an existing item in the world
    /// </summary>
    public void RepositionWorldItem(string itemId, Vector3 position, Quaternion rotation)
    {
        if (progressManager != null)
        {
            // The progression manager handles the details
            Debug.Log($"[WorldItemManager] Repositioning item {itemId} to {position}");
            // In a real implementation, you'd need to find the item and move it
        }
    }
}

/// <summary>
/// Simple component to show recovery position in the editor
/// </summary>
public class RecoveryPositionGizmo : MonoBehaviour
{
    private void OnDrawGizmos()
    {
        Gizmos.color = Color.green;
        Gizmos.DrawWireSphere(transform.position, 2f);
        Gizmos.DrawWireCube(transform.position, Vector3.one * 0.5f);
    }
    
    private void OnDrawGizmosSelected()
    {
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, 5f);
    }
} 