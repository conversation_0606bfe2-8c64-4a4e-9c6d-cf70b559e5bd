using UnityEngine;
using System.Collections;
using System.Collections.Generic;

public class StashSystem : MonoBehaviour
{
    [SerializeField] private InvUI inventoryUI;
    [SerializeField] private StashUI stashUI;
    [SerializeField] private EquipmentManager equipmentManager;
    [SerializeField] private PlayerStatus playerStatus;
    [SerializeField] private UIPanelManager uiPanelManager;
    [SerializeField] private ShopSystem shopSystem; // Add reference to ShopSystem
    [SerializeField] private PlayerProgressionManager progressionManager; // Reference to progression manager

    private InvItemContainer stashContainer;
    private bool isShowingTransferButton;
    private bool isInitializing = false;

    private const string STASH_SAVE_KEY = "player_stash_data";
    private const int STASH_WIDTH = 6;
    private const int BASE_ROW_COST = 5;

    private void Awake()
    {
        FindRequiredReferences();
    }

    private void Start()
    {
        // Initialize immediately without waiting for a frame
        InitializeStash();
        
        // Make sure stash is closed when starting
        if (uiPanelManager != null)
        {
            uiPanelManager.CloseStash();
        }
    }
    
    private void FindRequiredReferences()
    {
        if (equipmentManager == null)
            equipmentManager = GetComponent<EquipmentManager>();
            
        if (equipmentManager == null)
            equipmentManager = FindObjectOfType<EquipmentManager>();
        
        // Find UIPanelManager if not set
        if (uiPanelManager == null)
            uiPanelManager = FindObjectOfType<UIPanelManager>();

        // Find PlayerStatus if not set
        if (playerStatus == null)
            playerStatus = FindObjectOfType<PlayerStatus>();
        
        // Find ShopSystem if not set
        if (shopSystem == null)
            shopSystem = FindObjectOfType<ShopSystem>();
            
        // Find StashUI if not set
        if (stashUI == null)
            stashUI = FindObjectOfType<StashUI>();
            
        // Find progression manager if not set
        if (progressionManager == null)
            progressionManager = PlayerProgressionManager.Instance;
            
        if (progressionManager == null)
            progressionManager = FindObjectOfType<PlayerProgressionManager>();
    }

    private void InitializeStash()
    {
        if (isInitializing) return;
        isInitializing = true;
        
        // Check for progression manager for stash rows data
        var progressionManager = PlayerProgressionManager.Instance;
        if (progressionManager == null)
        {
            Debug.LogError("No PlayerProgressionManager found!");
            isInitializing = false;
            return;
        }

        // Migrate old stash rows if present
        if (PlayerPrefs.HasKey("player_stash_rows"))
        {
            int oldRows = PlayerPrefs.GetInt("player_stash_rows", 3);
            progressionManager.MigrateStashRows(oldRows);
            PlayerPrefs.DeleteKey("player_stash_rows");
            Debug.Log($"[StashSystem] Migrated old stash rows: {oldRows}");
        }

        int width = STASH_WIDTH;
        int height = progressionManager.GetCurrentStashRows();

        // Create the container with the correct dimensions
        stashContainer = new InvItemContainer(width, height);

        if (stashContainer.GridWidth != width || stashContainer.GridHeight != height)
        {
            Debug.LogError($"[StashSystem] Grid dimensions mismatch! Got {stashContainer.GridWidth}x{stashContainer.GridHeight} but expected {width}x{height}");
        }

        // Load stash data in a more efficient way
        LoadStashData();
        
        // Update the UI after initialization
        if (stashUI != null)
        {
            stashUI.UpdateUI();
        }
        
        isInitializing = false;
    }

    public int CalculateNextRowCost()
    {
        var progressionManager = PlayerProgressionManager.Instance;
        if (progressionManager == null) return BASE_ROW_COST;
        int currentRows = progressionManager.GetCurrentStashRows();
        return BASE_ROW_COST * (1 << (currentRows - progressionManager.GetDefaultStashRows()));
    }

    public void UpgradeStashSize()
    {
        int cost = CalculateNextRowCost();
        
        // Use PlayerStatus for currency check
        if (playerStatus == null || !playerStatus.TrySpendCurrency(cost))
        {
            Debug.Log($"Not enough currency to upgrade stash. Need {cost}");
            return;
        }

        var progressionManager = PlayerProgressionManager.Instance;
        if (progressionManager == null) return;
        
        progressionManager.UpgradeStashSize();

        // Efficiently resize the container while keeping existing items
        ResizeStashContainer(STASH_WIDTH, progressionManager.GetCurrentStashRows());
        
        // Save the stash data after resize
        SaveStashData();

        // Update UI
        if (stashUI != null)
            stashUI.UpdateUI();
    }
    
    private void ResizeStashContainer(int newWidth, int newHeight)
    {
        if (stashContainer == null) return;
        
        var newContainer = new InvItemContainer(newWidth, newHeight);
        
        // Copy all existing items to the new container
        foreach (var stack in stashContainer.GetItems())
        {
            if (stack != null && stashContainer.TryGetItemPosition(stack, out Vector2Int pos, out bool isRotated))
            {
                // Only add if position is within new bounds
                if (pos.x < newWidth && pos.y < newHeight)
                {
                    newContainer.AddItemToSlot(stack.Item, stack.Quantity, pos, isRotated);
                }
            }
        }
        
        stashContainer = newContainer;
    }

    public void SaveStashData()
    {
        if (stashContainer == null) return;
        
        var serializableList = new SerializableItemList();
        
        // Preallocate the list capacity for better performance
        serializableList.items = new List<SerializableItem>(stashContainer.GetItems().Count);
        
        foreach (var stack in stashContainer.GetItems())
        {
            if (stack != null && stashContainer.TryGetItemPosition(stack, out Vector2Int pos, out bool isRotated))
            {
                serializableList.items.Add(new SerializableItem
                {
                    itemName = stack.Item.itemName,
                    quantity = stack.Quantity,
                    gridPosition = pos,
                    isRotated = isRotated
                });
            }
        }
        
        // Update UI if needed
        if (stashUI != null)
        {
            stashUI.UpdateUI();
        }
        
        // Serialize and save
        string serializedData = JsonUtility.ToJson(serializableList);
        PlayerPrefs.SetString(STASH_SAVE_KEY, serializedData);
        PlayerPrefs.Save();
        
        // Also trigger the player progression manager to save
        if (progressionManager != null)
        {
            progressionManager.SaveInventoryAndEquipmentNow();
        }
    }

    private void LoadStashData()
    {
        string serializedData = PlayerPrefs.GetString(STASH_SAVE_KEY, "{}");
        
        try
        {
            var itemList = JsonUtility.FromJson<SerializableItemList>(serializedData);
            if (itemList?.items != null)
            {
                // Preload all items in batch for better performance
                foreach (var serializableItem in itemList.items)
                {
                    Item item = ItemDatabase.GetItemByName(serializableItem.itemName);
                    if (item != null)
                    {
                        stashContainer.AddItemToSlot(
                            item,
                            serializableItem.quantity,
                            serializableItem.gridPosition,
                            serializableItem.isRotated
                        );
                    }
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error loading stash data: {e.Message}");
        }
    }

    public void AddItemToStash(Item item, int quantity, Vector2Int position)
    {
        if (stashContainer == null || item == null) return;
        
        bool added = stashContainer.AddItemToSlot(item, quantity, position);
        
        if (added)
        {
            // Save stash data and trigger progression save
            SaveStashData();
            
            // Update UI
            if (stashUI != null)
            {
                stashUI.UpdateUI();
            }
        }
    }

    public InvItemContainer GetStashContainer() => stashContainer;
    public bool IsShowingTransferButton() => isShowingTransferButton;

    public void HandleStashUpgrade(int newRowCount)
    {
        ResizeStashContainer(STASH_WIDTH, newRowCount);
        SaveStashData();

        if (stashUI != null)
            stashUI.UpdateUI();
    }

    // Add this method for backward compatibility with existing code
    // This redirects to ShopSystem's sell container
    public InvItemContainer GetSellContainer()
    {
        // Try to get sell container from ShopSystem
        if (shopSystem != null)
        {
            return shopSystem.GetSellContainer();
        }
        
        // If ShopSystem not available, try to find it
        if (shopSystem == null)
        {
            shopSystem = FindObjectOfType<ShopSystem>();
            if (shopSystem != null)
            {
                return shopSystem.GetSellContainer();
            }
        }
        
        // Log a warning if we can't find ShopSystem
        Debug.LogWarning("ShopSystem not found, unable to get sell container");
        return null;
    }

    public bool IsSellInterfaceVisible()
    {
        return shopSystem != null && shopSystem.IsInSellMode();
    }
}
