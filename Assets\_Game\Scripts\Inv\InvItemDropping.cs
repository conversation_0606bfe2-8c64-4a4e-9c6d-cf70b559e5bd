using UnityEngine;

public class InvItemDropping : MonoBehaviour
{
    [SerializeField] private GameObject itemDropPrefab; // Fallback prefab for items without WorldModelPrefab
    [SerializeField] private EquipmentManager equipmentManager;
    [SerializeField] private PersistenceManager progressionManager;
    
    // Optional model prefab dictionary to override item-specific models
    [SerializeField] private bool useGlobalModelOverrides = false;
    [SerializeField] private ItemModelDictionary modelOverrides;
    
    [System.Serializable]
    public class ItemModelDictionary
    {
        [System.Serializable]
        public class ItemModelPair
        {
            public string itemName;
            public GameObject modelPrefab;
        }
        
        public ItemModelPair[] pairs;
        
        public GameObject GetModelForItem(string itemName)
        {
            if (pairs == null) return null;
            
            foreach (var pair in pairs)
            {
                if (pair.itemName == itemName)
                {
                    return pair.modelPrefab;
                }
            }
            
            return null;
        }
    }
    
    public float throwForce = 8f;
    public float throwUpwardForce = 2f;
    public float dropOffset = 1.5f;

    private Camera playerCamera;
    
    /// <summary>
    /// Get the item drop prefab for spawning items
    /// </summary>
    public GameObject GetItemDropPrefab()
    {
        return itemDropPrefab;
    }

    private void Start()
    {
        // Find the player camera reference
        if (Camera.main != null)
        {
            playerCamera = Camera.main;
        }
        else
        {
            // Try to find it by tag
            GameObject cameraObj = GameObject.FindGameObjectWithTag("MainCamera");
            if (cameraObj != null)
            {
                playerCamera = cameraObj.GetComponent<Camera>();
            }
        }
        
        if (playerCamera == null)
        {
            Debug.LogWarning("InvItemDropping: Could not find main camera. Using fallback for drop positions.");
        }
        
        // Check equipment reference
        if (equipmentManager == null)
        {
            equipmentManager = FindObjectOfType<EquipmentManager>();
            if (equipmentManager == null)
            {
                Debug.LogError("InvItemDropping: EquipmentManager reference is missing!");
            }
        }
        
        // Check progression manager
        if (progressionManager == null)
        {
            progressionManager = PersistenceManager.Instance;
            if (progressionManager == null)
            {
                Debug.LogError("InvItemDropping: PlayerProgressionManager reference is missing!");
            }
        }
    }
    
    private GameObject SpawnItemPrefab(Item itemToDrop, Vector3 position, Quaternion rotation)
    {
        GameObject prefabToSpawn;
            
        // Check for model override
        if (useGlobalModelOverrides && modelOverrides != null)
        {
            GameObject overrideModel = modelOverrides.GetModelForItem(itemToDrop.itemName);
            if (overrideModel != null)
            {
                prefabToSpawn = overrideModel;
            }
            else if (itemToDrop.WorldModelPrefab != null)
            {
                prefabToSpawn = itemToDrop.WorldModelPrefab;
            }
            else
            {
                // Fallback to generic prefab
                prefabToSpawn = itemDropPrefab;
            }
        }
        else if (itemToDrop.WorldModelPrefab != null)
        {
            // Use the item's own world model prefab directly
            prefabToSpawn = itemToDrop.WorldModelPrefab;
        }
        else
        {
            // Fallback to generic prefab
            prefabToSpawn = itemDropPrefab;
        }
        
        // Instantiate the prefab
        GameObject droppedItem = Instantiate(prefabToSpawn, position, rotation);
        
        // Make sure it has the required components for a dropped item
        EnsureRequiredComponents(droppedItem, itemToDrop);
        
        // Remove any model swapper components - we want to use the actual prefab directly
        InvItemModelSwapper modelSwapper = droppedItem.GetComponent<InvItemModelSwapper>();
        if (modelSwapper != null)
        {
            Destroy(modelSwapper);
        }
        
        return droppedItem;
    }
    
    private void EnsureRequiredComponents(GameObject droppedItem, Item item)
    {
        // Make sure it has a Rigidbody
        Rigidbody rb = droppedItem.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = droppedItem.AddComponent<Rigidbody>();
            
            // Use default physics settings - the prefab should have proper physics already
            rb.mass = 1f;
            rb.linearDamping = 0.5f;
            rb.angularDamping = 0.2f;
            rb.collisionDetectionMode = CollisionDetectionMode.Continuous;
            rb.isKinematic = false;
            rb.useGravity = true;
        }
        
        // Make sure it has a collider
        Collider collider = droppedItem.GetComponent<Collider>();
        if (collider == null)
        {
            // Check if any child has a collider
            Collider[] childColliders = droppedItem.GetComponentsInChildren<Collider>();
            if (childColliders.Length == 0)
            {
                // Add a box collider as fallback
                BoxCollider boxCollider = droppedItem.AddComponent<BoxCollider>();
                boxCollider.size = new Vector3(0.5f, 0.5f, 0.5f);
                boxCollider.center = Vector3.zero;
            }
        }
        
        // Make sure it has InvItemPickup component
        InvItemPickup itemPickup = droppedItem.GetComponent<InvItemPickup>();
        if (itemPickup == null)
        {
            itemPickup = droppedItem.AddComponent<InvItemPickup>();
        }
    }
    
    public void ThrowItem(Item itemToBeThrown, int quantity = 1, bool isEquipment = false)
    {
        // ... existing code (keep as is) ...
        
        // ... at the point where you instantiate the prefab, replace with:
        GameObject playerObject = gameObject;
        Vector3 dropPosition;
        Vector3 dropDirection;
        
        // Get drop position and direction
        if (playerCamera != null)
        {
            dropPosition = playerCamera.transform.position + playerCamera.transform.forward * dropOffset;
            dropDirection = playerCamera.transform.forward;
        }
        else
        {
            // Fallback
            dropPosition = transform.position + transform.forward * dropOffset;
            dropPosition.y += 1.5f;
            dropDirection = transform.forward;
        }
        
        // Now use the new method to spawn the actual prefab
        GameObject droppedItemInstance = SpawnItemPrefab(itemToBeThrown, dropPosition, Quaternion.identity);
        
        // Get or add the item pickup component
        InvItemPickup invItemPickup = droppedItemInstance.GetComponent<InvItemPickup>();
        
        // ... rest of the throw logic (set item, apply force, etc.)
        if (invItemPickup != null)
        {
            invItemPickup.SetItem(itemToBeThrown, quantity);
            
            // Handle storage equipment if needed
            if (isEquipment && itemToBeThrown is Bag)
            {
                var droppedStorage = droppedItemInstance.GetComponent<InvDroppedStorageEquipment>();
                if (droppedStorage == null)
                {
                    droppedStorage = droppedItemInstance.AddComponent<InvDroppedStorageEquipment>();
                }
            }
            
            // Mark as player-dropped for persistence
            invItemPickup.MarkAsPlayerDropped();

            // Apply physics force
            Rigidbody rb = droppedItemInstance.GetComponent<Rigidbody>();
            if (rb != null)
            {
                // Calculate throw force
                Vector3 force = dropDirection * throwForce + Vector3.up * throwUpwardForce;
                
                // Add player velocity if available
                Rigidbody playerRb = playerObject.GetComponent<Rigidbody>();
                if (playerRb != null)
                {
                    force += playerRb.linearVelocity * 0.5f;
                }
                
                // Apply force
                rb.isKinematic = false;
                rb.linearVelocity = Vector3.zero;
                rb.AddForce(force, ForceMode.Impulse);
                
                // Add slight random rotation
                rb.AddTorque(new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ).normalized * 2f, ForceMode.Impulse);
            }
        }
        else
        {
            Debug.LogError("InvItemPickup component not found on the spawned prefab.");
            Destroy(droppedItemInstance);
        }
    }

    public GameObject DropItem(Item itemToDrop, int quantity, bool isEquipment = false)
    {
        if (itemToDrop == null)
        {
            Debug.LogError("Attempted to drop null item");
            return null;
        }
        
        bool isStorageItem = isEquipment && itemToDrop is Bag;
        EquipmentBase itemToBeDropped = isEquipment ? (EquipmentBase)itemToDrop : null;
        
        // Get drop position from camera
        Vector3 dropPosition;
        Vector3 dropDirection;
        
        if (playerCamera != null)
        {
            dropPosition = playerCamera.transform.position + playerCamera.transform.forward * dropOffset;
            dropDirection = playerCamera.transform.forward;
        }
        else
        {
            // Fallback
            dropPosition = transform.position + transform.forward * dropOffset;
            dropPosition.y += 1.5f;
            dropDirection = transform.forward;
        }
        
        // Handle any serialization needed for storage items
        string serializedContent = "";
        if (isStorageItem)
        {
            // If dropping a storage item, we need to handle serializing its content
            EquipmentSlotType slotType = itemToBeDropped.Slot;
            
            // Only serialize if it's currently equipped
            var slot = equipmentManager.GetEquipmentSlot(slotType);
            if (slot?.equippedItem != null && slot.equippedItem == itemToBeDropped)
            {
                serializedContent = equipmentManager.SerializeContainerContent(slotType);
            }
            
            // Unequip the item if it's equipped
            if (slot?.equippedItem == itemToBeDropped)
            {
                equipmentManager.UnequipItem(slotType, true);
            }
        }
        
        // Spawn the actual item prefab
        GameObject droppedObject = SpawnItemPrefab(itemToDrop, dropPosition, Quaternion.identity);
        InvItemPickup droppedItemComponent = droppedObject.GetComponent<InvItemPickup>();
        
        if (droppedItemComponent != null)
        {
            droppedItemComponent.SetItem(itemToDrop, quantity);
            
            // If it's a storage item with content
            if (isStorageItem && !string.IsNullOrEmpty(serializedContent))
            {
                var storageComponent = droppedObject.GetComponent<InvDroppedStorageEquipment>();
                if (storageComponent == null)
                {
                    storageComponent = droppedObject.AddComponent<InvDroppedStorageEquipment>();
                }
                storageComponent.StorageContent = serializedContent;
            }
            
            // Apply physics force
            Rigidbody rb = droppedObject.GetComponent<Rigidbody>();
            if (rb != null)
            {
                // Calculate throw force
                Vector3 force = dropDirection * throwForce + Vector3.up * throwUpwardForce;
                
                // Apply force
                rb.isKinematic = false;
                rb.linearVelocity = Vector3.zero;
                rb.AddForce(force, ForceMode.Impulse);
                
                // Add slight random rotation
                rb.AddTorque(new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ).normalized * 2f, ForceMode.Impulse);
            }
            
            // Mark as player-dropped for persistence
            droppedItemComponent.MarkAsPlayerDropped();
            
            return droppedObject;
        }
        else
        {
            Debug.LogError("InvItemPickup component not found on spawned item prefab.");
            Destroy(droppedObject);
            return null;
        }
    }

    /// <summary>
    /// Creates a dropped item instance in the world from a string item name.
    /// Used by UI systems like InvDragAndDropManager for drag-drop operations.
    /// </summary>
    public void CreateDroppedItemInstance(
        GameObject playerObject,
        string itemName,
        Vector2 screenPosition,
        bool isEquipment,
        int equipmentSlotTypeInt,
        string serializedContainerContent,
        int quantity = 1)
    {
        // Validate item name
        if (string.IsNullOrEmpty(itemName))
        {
            Debug.LogError("Item name is null or empty. Cannot drop item.");
            return;
        }

        // Get the item data
        Item itemToBeDropped = ItemDatabase.GetItemByName(itemName);
        if (itemToBeDropped == null)
        {
            Debug.LogError($"Item with name '{itemName}' not found in database. Cannot drop item.");
            return;
        }
        
        // Get proper drop position and direction from camera
        Vector3 dropPosition;
        Vector3 dropDirection;
        
        if (playerCamera != null)
        {
            dropPosition = playerCamera.transform.position + playerCamera.transform.forward * dropOffset;
            dropDirection = playerCamera.transform.forward;
        }
        else
        {
            // Fallback
            dropPosition = playerObject.transform.position + playerObject.transform.forward * dropOffset;
            dropPosition.y += 1.5f;
            dropDirection = playerObject.transform.forward;
        }
        
        // Spawn the item prefab
        GameObject droppedItemInstance = SpawnItemPrefab(itemToBeDropped, dropPosition, Quaternion.identity);
        InvItemPickup invItemPickup = droppedItemInstance.GetComponent<InvItemPickup>();
        
        if (invItemPickup != null)
        {
            invItemPickup.SetItem(itemToBeDropped, quantity);
            
            // Mark the item as recently thrown to prevent immediate pickup
            invItemPickup.SetTimeSinceThrown(Time.time);
            
            // Handle storage equipment if needed
            if (isEquipment && itemToBeDropped is Bag)
            {
                var droppedStorage = droppedItemInstance.GetComponent<InvDroppedStorageEquipment>();
                if (droppedStorage == null)
                {
                    droppedStorage = droppedItemInstance.AddComponent<InvDroppedStorageEquipment>();
                }
                
                droppedStorage.SetContainerSnapshot(serializedContainerContent);
            }
            
            // Mark as player-dropped for persistence
            invItemPickup.MarkAsPlayerDropped();
            
            // Apply physics force
            Rigidbody rb = droppedItemInstance.GetComponent<Rigidbody>();
            if (rb != null)
            {
                // Calculate throw force
                Vector3 force = dropDirection * throwForce + Vector3.up * throwUpwardForce;
                
                // Add player velocity if available
                Rigidbody playerRb = playerObject.GetComponent<Rigidbody>();
                if (playerRb != null)
                {
                    force += playerRb.linearVelocity * 0.5f;
                }
                
                // Apply force
                rb.isKinematic = false;
                rb.linearVelocity = Vector3.zero;
                rb.AddForce(force, ForceMode.Impulse);
                
                // Add slight random rotation
                rb.AddTorque(new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ).normalized * 2f, ForceMode.Impulse);
            }
        }
        else
        {
            Debug.LogError("InvItemPickup component not found on the spawned prefab.");
            Destroy(droppedItemInstance);
        }
    }
}