using System;
using System.Linq;
using UnityEngine;
using System.Collections.Generic;

public class EquipmentManager : MonoBehaviour
{
    [System.Serializable]
    public class EquipmentSlot
    {
        public EquipmentSlotType slotType;
        public EquipmentBase equippedItem;
        public InvItemContainer storageContainer;
        public string serializedContainer;
    }

    [SerializeField]
    private List<EquipmentSlot> equipmentSlots;

    public event Action<EquipmentBase, EquipmentSlotType> OnEquipmentChanged;

    private PlayerStatus playerStatus;
    private PersistentObject progressionManager;

    public IEnumerable<EquipmentSlot> GetEquipmentSlots()
    {
        return equipmentSlots;
    }

    private void Awake()
    {
        InitializeEquipmentSlots();
    }

    private void Start()
    {
        playerStatus = GetComponent<PlayerStatus>();
        if (playerStatus == null)
        {
            Debug.LogError("PlayerStatus component not found on the same GameObject!");
        }

        // Get the progression manager
        progressionManager = PlayerProgressionManager.Instance;
        if (progressionManager == null)
        {
            progressionManager = FindObjectOfType<PlayerProgressionManager>();
        }
    }

    private void InitializeEquipmentSlots()
    {
        equipmentSlots = new List<EquipmentSlot>
        {
            new EquipmentSlot { slotType = EquipmentSlotType.HeadSlot },
            new EquipmentSlot { slotType = EquipmentSlotType.ChestSlot },
            new EquipmentSlot { slotType = EquipmentSlotType.BagSlot },
        };
    }

    public bool RemoveItemFromContainer(EquipmentSlotType containerSlotType, int index)
    {
        var slot = GetEquipmentSlot(containerSlotType);
        if (slot?.storageContainer == null)
        {
            Debug.LogWarning($"RemoveItemFromContainer: Invalid container for slot type {containerSlotType}");
            return false;
        }
        Debug.Log($"Removing item from {containerSlotType} at index {index}");
        Vector2Int pos = ContainerGrid.IndexToGridPosition(index, slot.storageContainer.GridWidth);
        var stack = slot.storageContainer.GetStackAtPosition(pos);
        if (stack == null || stack.Item == null)
        {
            Debug.LogWarning($"No item found at position {pos} (index {index})");
            return false;
        }

        // Store a reference to the item before removing it
        Item removedItem = stack.Item;
        bool isTool = false;

        // Check if the item is a tool before removing it
        var toolSelectionManager = UnityEngine.Object.FindObjectOfType<ToolSelectionManager>();
        if (toolSelectionManager != null)
        {
            // Use reflection to call IsItemATool since it's private
            var isItemAToolMethod = typeof(ToolSelectionManager).GetMethod("IsItemATool",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (isItemAToolMethod != null)
            {
                isTool = (bool)isItemAToolMethod.Invoke(toolSelectionManager, new object[] { removedItem });
            }
        }

        // Remove the item
        slot.storageContainer.SetItemInSlot(index, null);
        UpdateWeightAfterItemChange();
        ForceImmediateUIUpdate();

        // Save equipment state after removing an item
        SaveEquipmentState();

        // If the removed item was a tool, force a scan to update the tool selection
        if (isTool && toolSelectionManager != null)
        {
            Debug.Log($"[EquipmentManager] Removed tool {removedItem.itemName}, updating tool selection");
            toolSelectionManager.ForceToolScan(false);
        }

        return true;
    }

    public bool AddItemToContainerSlot(EquipmentSlotType containerSlotType, Item item, int quantity, int targetIndex, bool isRotated = false)
    {
        var slot = GetEquipmentSlot(containerSlotType);
        if (slot?.equippedItem == null) return false;

        if (slot.storageContainer == null || slot.storageContainer.MaxSize == 0)
        {
            InitializeStorageContainer(slot, slot.equippedItem);
        }
        if (slot.storageContainer == null) return false;

        Vector2Int gridPos = ContainerGrid.IndexToGridPosition(targetIndex, slot.storageContainer.GridWidth);
        bool added = slot.storageContainer.AddItemToSlot(item, quantity, gridPos, isRotated);

        if (added)
        {
            UpdateWeightAfterItemChange();
            ForceImmediateUIUpdate();

            // Save equipment state after adding an item
            SaveEquipmentState();

            // Notify the ToolSelectionManager about the new item
            var toolSelectionManager = UnityEngine.Object.FindObjectOfType<ToolSelectionManager>();
            if (toolSelectionManager != null)
            {
                // Check if the item is a tool and update the tool selection
                toolSelectionManager.CheckNewItem(item, true);
            }
        }
        return added;
    }

    public bool MoveItemWithinContainer(EquipmentSlotType containerSlotType, int sourceIndex, int targetIndex, bool isRotated)
    {
        var slot = GetEquipmentSlot(containerSlotType);
        if (slot?.storageContainer == null) return false;

        Vector2Int sourcePos = ContainerGrid.IndexToGridPosition(sourceIndex, slot.storageContainer.GridWidth);
        Vector2Int targetPos = ContainerGrid.IndexToGridPosition(targetIndex, slot.storageContainer.GridWidth);

        // Get the item stack at the source position
        var stack = slot.storageContainer.GetStackAtPosition(sourcePos);
        if (stack == null) return false;

        Debug.Log($"Moving item {stack.Item.itemName} within container from {sourcePos} to {targetPos}, rotation: {isRotated}");

        // Flag to track if we need to ensure container persists when moving a bag
        bool isMovingBag = stack.Item is Bag;

        // Perform the move
        bool moved = slot.storageContainer.MoveItemWithinContainer(sourceIndex, targetIndex, isRotated);

        if (moved)
        {
            // When moving a bag inside a container, ensure we treat it as a regular item
            // and don't let it affect the equipped bag in any way
            if (isMovingBag)
            {
                Debug.Log($"Successfully moved bag item within container without affecting equipment");
            }

            // Save equipment state after moving an item
            SaveEquipmentState();
        }

        return moved;
    }

    public string SerializeContainerContent(EquipmentSlotType slotType)
    {
        var slot = GetEquipmentSlot(slotType);
        if (slot?.storageContainer != null)
        {
            return SerializeContainer(slot.storageContainer);
        }
        return "";
    }

    public void DeserializeContainerContent(EquipmentSlotType slotType, string serializedContent)
    {
        var slot = GetEquipmentSlot(slotType);
        if (slot != null)
        {
            slot.storageContainer = DeserializeContainer(serializedContent);
        }
    }

    public EquipmentBase GetEquippedItem(EquipmentSlotType slotType)
    {
        var slot = GetEquipmentSlot(slotType);
        return slot?.equippedItem;
    }

    public EquipmentSlot GetEquipmentSlot(EquipmentSlotType slotType)
        => equipmentSlots?.Find(slot => slot.slotType == slotType);

    public bool EquipItem(EquipmentBase newItem)
    {
        if (newItem == null) return false;
        var slot = GetEquipmentSlot(newItem.Slot);
        if (slot == null) return false;
        EquipmentBase oldItem = slot.equippedItem;

        // If there's an item already equipped in this slot
        if (oldItem != null)
        {
            // Try to move the old item to inventory first
            bool movedToInventory = TryMoveToInventory(oldItem);
            if (!movedToInventory)
            {
                Debug.LogWarning($"Could not move existing {oldItem.itemName} to inventory. Not enough space.");
                // We won't prevent equipping if we can't move to inventory,
                // but we'll log a warning since the old item will be lost
            }

            // Unequip the old item
            UnequipItem(slot.slotType);
        }

        slot.equippedItem = newItem;
        newItem.Equip(this);

        // Always create a new container when equipping a Bag to ensure no leftover content
        if (newItem is Bag bag)
        {
            // Create a fresh container based on the bag's dimensions
            int width = bag.GridWidth;
            int height = bag.GridHeight;
            slot.storageContainer = new InvItemContainer(width, height);
            Debug.Log($"Created fresh container for newly equipped bag: {newItem.itemName} ({width}x{height})");
        }

        OnEquipmentChanged?.Invoke(newItem, slot.slotType);
        playerStatus?.UpdateWeight();

        // If item is armor, trigger max energy recalculation
        if (newItem is Armor)
        {
            playerStatus?.RecalculateMaxEnergy();
        }

        // Save equipment state after equipping an item
        SaveEquipmentState();

        return true;
    }

    // New method to attempt moving an item to the player's inventory
    private bool TryMoveToInventory(Item item)
    {
        if (item == null) return false;

        // Find the first available storage container
        var containers = GetEquipmentSlots()
            .Where(s => s.equippedItem != null && s.equippedItem is Bag && s.storageContainer != null)
            .Select(s => s.storageContainer)
            .ToList();

        if (containers.Count == 0) return false;

        // Try to add to the first bag with space
        foreach (var container in containers)
        {
            var position = container.FindSpaceForItem(item);
            if (position.HasValue)
            {
                bool added = container.AddItemToSlot(item, 1, position.Value, false);
                if (added)
                {
                    Debug.Log($"Successfully moved {item.itemName} to inventory container");
                    return true;
                }
            }
        }

        Debug.LogWarning($"No space found in any container for {item.itemName}");
        return false;
    }

    private System.Collections.IEnumerator ForceStorageUIRefresh()
    {
        ForceImmediateUIUpdate();
        yield return null;
        ForceImmediateUIUpdate();
        yield return null;
        ForceImmediateUIUpdate();

        // Final explicit update with both methods to ensure complete refresh
        InvUI invUI = FindObjectOfType<InvUI>();
        if (invUI != null)
        {
            invUI.UpdateStorageDisplay();
            invUI.UpdateUI();
        }
    }

    private void InitializeStorageContainer(EquipmentSlot slot, EquipmentBase equipment)
    {
        if (slot == null || equipment == null) return;
        (int width, int height) = GetStorageGridDimensions(equipment);
        if (width <= 0 || height <= 0)
        {
            slot.storageContainer = null;
            return;
        }
        if (slot.storageContainer == null ||
            slot.storageContainer.GridWidth != width ||
            slot.storageContainer.GridHeight != height)
        {
            var newContainer = new InvItemContainer(width, height);

            if (slot.storageContainer != null)
            {
                var oldItems = slot.storageContainer.GetItems();
                if (oldItems != null && oldItems.Any())
                {
                    foreach (var stack in oldItems)
                    {
                        if (stack != null && slot.storageContainer.TryGetItemPosition(stack, out Vector2Int position, out bool isRotated))
                        {
                            newContainer.AddItemToSlot(stack.Item, stack.Quantity, position, isRotated);
                        }
                    }
                }
            }
            slot.storageContainer = newContainer;
        }
    }

    private (int width, int height) GetStorageGridDimensions(EquipmentBase equipment)
    {
        return equipment switch
        {
            Bag bag => (bag.GridWidth, bag.GridHeight),
            _ => (0, 0)
        };
    }

    public void ForceImmediateUIUpdate()
    {
        InvUI invUI = FindObjectOfType<InvUI>();
        if (invUI != null)
        {
            // Set the equipment manager reference on the UI before updating
            // This helps recover from scene transitions where references can be lost
            var equipmentManagerField = typeof(InvUI).GetField("equipmentManager",
                System.Reflection.BindingFlags.Instance |
                System.Reflection.BindingFlags.Public |
                System.Reflection.BindingFlags.NonPublic);

            if (equipmentManagerField != null)
            {
                object currentValue = equipmentManagerField.GetValue(invUI);
                if (currentValue == null)
                {
                    Debug.Log("Updating null equipmentManager reference in InvUI");
                    equipmentManagerField.SetValue(invUI, this);
                }
            }

            // Now update the UI
            invUI.UpdateUI();
            invUI.UpdateStorageDisplay();
        }
    }

    private string SerializeContainer(InvItemContainer container)
    {
        if (container == null) return "{}";
        var serializableList = new SerializableItemList
        {
            items = container.GetItems()
                .Where(stack => stack != null && stack.Item != null)
                .Select(stack =>
                {
                    container.TryGetItemPosition(stack, out Vector2Int pos, out bool isRot);
                    return new SerializableItem
                    {
                        itemName = stack.Item.itemName,
                        quantity = stack.Quantity,
                        slotIndex = pos.y * container.GridWidth + pos.x,
                        gridPosition = pos,
                        isRotated = isRot
                    };
                })
                .ToList()
        };
        return JsonUtility.ToJson(serializableList);
    }

    // Add a method to trigger saving equipment state
    private void SaveEquipmentState()
    {
        if (progressionManager != null)
        {
            // Slight delay to ensure all operations are completed
            Invoke(nameof(DelayedSave), 0.1f);
        }
    }

    private void DelayedSave()
    {
        progressionManager.SaveInventoryAndEquipmentNow();
    }

    public InvItemContainer DeserializeContainer(string serialized)
    {
        if (string.IsNullOrEmpty(serialized)) return null;
        try
        {
            var itemList = JsonUtility.FromJson<SerializableItemList>(serialized);
            if (itemList?.items != null)
            {
                int maxX = 0;
                int maxY = 0;
                foreach (var it in itemList.items)
                {
                    if (it.gridPosition.x > maxX) maxX = it.gridPosition.x;
                    if (it.gridPosition.y > maxY) maxY = it.gridPosition.y;
                }
                int width = maxX + 1;
                int height = maxY + 1;
                var container = new InvItemContainer(width, height);

                foreach (var serializableItem in itemList.items)
                {
                    if (!string.IsNullOrEmpty(serializableItem.itemName))
                    {
                        Item item = ItemDatabase.GetItemByName(serializableItem.itemName);
                        if (item != null)
                        {
                            container.AddItemToSlot(item, serializableItem.quantity,
                                serializableItem.gridPosition, serializableItem.isRotated);
                        }
                    }
                }
                return container;
            }
        }
        catch (Exception e)
        {
            Debug.LogError($"Error deserializing container: {e.Message}\n{e.StackTrace}");
        }
        return null;
    }

    public void UpdateWeightAfterItemChange()
    {
        playerStatus?.UpdateWeight();
    }

    public void UnequipItem(EquipmentSlotType slotType, bool isDropping = false)
    {
        var slot = GetEquipmentSlot(slotType);
        if (slot == null || slot.equippedItem == null) return;

        EquipmentBase oldItem = slot.equippedItem;
        slot.equippedItem = null;

        OnEquipmentChanged?.Invoke(null, slotType);
        playerStatus?.UpdateWeight();

        // If item was armor, trigger max energy recalculation
        if (oldItem is Armor)
        {
            playerStatus?.RecalculateMaxEnergy();
        }

        // If this is a bag and we're dropping it, don't clear the container
        // as it gets serialized to the world item
        if (!(isDropping && oldItem is Bag))
        {
            slot.storageContainer = null;
        }

        // Save equipment state after unequipping an item
        SaveEquipmentState();
    }

    public void UnequipItemInstance(EquipmentBase equipmentInstance)
    {
        var slot = equipmentSlots?.Find(s => s.equippedItem == equipmentInstance);
        if (slot != null)
        {
            UnequipItem(slot.slotType);
        }
    }

    public void UpdateWeightCapacity(float newCapacity)
    {
        // Update the weight capacity of the player
        PlayerStatus playerStatus = GetComponent<PlayerStatus>();
        if (playerStatus != null)
        {
            playerStatus.UpdateWeightCapacityBonus();
        }
    }
}
