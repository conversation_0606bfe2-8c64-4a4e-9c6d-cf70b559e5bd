Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.1f1 (7197418f847b) revision 7444289'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 65462 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-04T00:29:10Z

COMMAND LINE ARGUMENTS:
C:\Unity\Editors\6000.1.1f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Unity/BLAME/BLAME
-logFile
Logs/AssetImportWorker2.log
-srvPort
59354
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Unity/BLAME/BLAME
C:/Unity/BLAME/BLAME
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [217904]  Target information:

Player connection [217904]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1195731252 [EditorId] 1195731252 [Version] 1048832 [Id] WindowsEditor(7,KINO) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [217904] Host joined multi-casting on [***********:54997]...
Player connection [217904] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2602.02 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.1f1 (7197418f847b)
[Subsystems] Discovering subsystems at path C:/Unity/Editors/6000.1.1f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Unity/BLAME/BLAME/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3060 Ti (ID=0x2489)
    Vendor:   NVIDIA
    VRAM:     8024 MB
    Driver:   32.0.15.7688
Initialize mono
Mono path[0] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/Managed'
Mono path[1] = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Unity/Editors/6000.1.1f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56780
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Unity/Editors/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.020709 seconds.
- Loaded All Assemblies, in  2.092 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.275 seconds
Domain Reload Profiling: 4319ms
	BeginReloadAssembly (614ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (252ms)
	RebuildNativeTypeToScriptingClass (45ms)
	initialDomainReloadingComplete (271ms)
	LoadAllAssembliesAndSetupDomain (860ms)
		LoadAssemblies (545ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (822ms)
			TypeCache.Refresh (819ms)
				TypeCache.ScanAssembly (748ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (2277ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1985ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (225ms)
			SetLoadedEditorAssemblies (47ms)
			BeforeProcessingInitializeOnLoad (720ms)
			ProcessInitializeOnLoadAttributes (629ms)
			ProcessInitializeOnLoadMethodAttributes (362ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Launched and connected shader compiler UnityShaderCompiler.exe after 0.10 seconds
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 13.167 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 50.85 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
[MODES] Loading mode Default (0) for mode-current-id-Persistent Object
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.408 seconds
Domain Reload Profiling: 15501ms
	BeginReloadAssembly (8038ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (101ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (345ms)
	RebuildNativeTypeToScriptingClass (41ms)
	initialDomainReloadingComplete (297ms)
	LoadAllAssembliesAndSetupDomain (4371ms)
		LoadAssemblies (3404ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1375ms)
			TypeCache.Refresh (842ms)
				TypeCache.ScanAssembly (814ms)
			BuildScriptInfoCaches (445ms)
			ResolveRequiredComponents (76ms)
	FinalizeReload (2409ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1953ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (440ms)
			ProcessInitializeOnLoadAttributes (734ms)
			ProcessInitializeOnLoadMethodAttributes (736ms)
			AfterProcessingInitializeOnLoad (32ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (37ms)
Refreshing native plugins compatible for Editor in 30.90 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 553 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7912 unused Assets / (3.7 MB). Loaded Objects now: 8979.
Memory consumption went from 384.0 MB to 380.2 MB.
Total: 22.337200 ms (FindLiveObjects: 1.394300 ms CreateObjectMapping: 2.677300 ms MarkObjects: 13.427600 ms  DeleteObjects: 4.836200 ms)

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.299 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 68.98 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.467 seconds
Domain Reload Profiling: 6707ms
	BeginReloadAssembly (1247ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (162ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (129ms)
	RebuildCommonClasses (130ms)
	RebuildNativeTypeToScriptingClass (31ms)
	initialDomainReloadingComplete (114ms)
	LoadAllAssembliesAndSetupDomain (2716ms)
		LoadAssemblies (2646ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (841ms)
			TypeCache.Refresh (54ms)
				TypeCache.ScanAssembly (23ms)
			BuildScriptInfoCaches (729ms)
			ResolveRequiredComponents (43ms)
	FinalizeReload (2468ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1887ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (743ms)
			ProcessInitializeOnLoadAttributes (877ms)
			ProcessInitializeOnLoadMethodAttributes (213ms)
			AfterProcessingInitializeOnLoad (45ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (60ms)
Refreshing native plugins compatible for Editor in 622.93 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 35 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9002.
Memory consumption went from 320.4 MB to 317.5 MB.
Total: 1742.553500 ms (FindLiveObjects: 40.312100 ms CreateObjectMapping: 536.188300 ms MarkObjects: 896.034000 ms  DeleteObjects: 270.017400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 547161.172789 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/BatteryNotificationTemplate.uxml
  artifactKey: Guid(7a8a31df1e53c374e8aeccc92a12e52a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/BatteryNotificationTemplate.uxml using Guid(7a8a31df1e53c374e8aeccc92a12e52a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: 'cc424780afe3b3885452e0cf81094bce') in 0.1381062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/NotificationTemplate.uxml
  artifactKey: Guid(db6a9787d6cbf2f43ab3fefd11abeee1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/NotificationTemplate.uxml using Guid(db6a9787d6cbf2f43ab3fefd11abeee1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: 'cf7db956ae02fa80280494506f8e9b74') in 0.0367573 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 3

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.652 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 30.43 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  7.460 seconds
Domain Reload Profiling: 12004ms
	BeginReloadAssembly (1527ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (230ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (286ms)
	RebuildCommonClasses (167ms)
	RebuildNativeTypeToScriptingClass (44ms)
	initialDomainReloadingComplete (177ms)
	LoadAllAssembliesAndSetupDomain (2629ms)
		LoadAssemblies (2654ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (597ms)
			TypeCache.Refresh (25ms)
				TypeCache.ScanAssembly (9ms)
			BuildScriptInfoCaches (525ms)
			ResolveRequiredComponents (36ms)
	FinalizeReload (7461ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (6486ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (74ms)
			BeforeProcessingInitializeOnLoad (1697ms)
			ProcessInitializeOnLoadAttributes (4137ms)
			ProcessInitializeOnLoadMethodAttributes (507ms)
			AfterProcessingInitializeOnLoad (69ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (162ms)
Refreshing native plugins compatible for Editor in 2552.67 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9007.
Memory consumption went from 317.8 MB to 314.9 MB.
Total: 186.288700 ms (FindLiveObjects: 1.828800 ms CreateObjectMapping: 7.707300 ms MarkObjects: 143.321100 ms  DeleteObjects: 33.430000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.929 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.46 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.236 seconds
Domain Reload Profiling: 6142ms
	BeginReloadAssembly (820ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (83ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (274ms)
	RebuildCommonClasses (162ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (3838ms)
		LoadAssemblies (3145ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (876ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (823ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1237ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (914ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (241ms)
			ProcessInitializeOnLoadAttributes (520ms)
			ProcessInitializeOnLoadMethodAttributes (122ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 26.91 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (5.2 MB). Loaded Objects now: 9009.
Memory consumption went from 317.7 MB to 312.5 MB.
Total: 17.457900 ms (FindLiveObjects: 1.533300 ms CreateObjectMapping: 1.200500 ms MarkObjects: 10.340300 ms  DeleteObjects: 4.380700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.823 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 62.76 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.930 seconds
Domain Reload Profiling: 6673ms
	BeginReloadAssembly (1058ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (140ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (2ms)
		CreateAndSetChildDomain (273ms)
	RebuildCommonClasses (132ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (176ms)
	LoadAllAssembliesAndSetupDomain (3353ms)
		LoadAssemblies (3273ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (463ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (395ms)
			ResolveRequiredComponents (25ms)
	FinalizeReload (1931ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1502ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (17ms)
			BeforeProcessingInitializeOnLoad (517ms)
			ProcessInitializeOnLoadAttributes (746ms)
			ProcessInitializeOnLoadMethodAttributes (192ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (47ms)
Refreshing native plugins compatible for Editor in 127.74 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7985 unused Assets / (4.0 MB). Loaded Objects now: 9011.
Memory consumption went from 318.2 MB to 314.3 MB.
Total: 17.819200 ms (FindLiveObjects: 1.306500 ms CreateObjectMapping: 1.062300 ms MarkObjects: 11.417300 ms  DeleteObjects: 4.031100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 74112.344501 seconds.
  path: Assets/_Game/Audio/Player/concrete
  artifactKey: Guid(14adce3abce7fe844860b5eb0bcf0775) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Audio/Player/concrete using Guid(14adce3abce7fe844860b5eb0bcf0775) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04e3dae458c2fb3a3c0786d3885f5eb7') in 0.5609128 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 23.974487 seconds.
  path: Assets/_Game/Resources/Audio/Events/Walk 1.asset
  artifactKey: Guid(24aae328cce3ade44be3455b92d02be0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Audio/Events/Walk 1.asset using Guid(24aae328cce3ade44be3455b92d02be0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8e09e0fdc0cf1ae415170e6534930347') in 0.0243402 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 6.376591 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_walk5.ogg
  artifactKey: Guid(6cc06c43596571b4c8b80f349a125067) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_walk5.ogg using Guid(6cc06c43596571b4c8b80f349a125067) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd33f303fb8741cfffde1464fb7712e2b') in 0.1842083 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through11.ogg
  artifactKey: Guid(7f84201e42e2bab4ca79c04411494650) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through11.ogg using Guid(7f84201e42e2bab4ca79c04411494650) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e493f43a8ceea804922659aa59baa577') in 0.0204708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_walk2.ogg
  artifactKey: Guid(9b66aecb4d8e5a5448ea8bd490840e96) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_walk2.ogg using Guid(9b66aecb4d8e5a5448ea8bd490840e96) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c64f56a6f964b52dbfc9ac3a95af5d0') in 0.0257494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_walk8.ogg
  artifactKey: Guid(d31e98157fd525c43b6bd330f63833fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_walk8.ogg using Guid(d31e98157fd525c43b6bd330f63833fa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a4770c458e34d86f51b38b05cc90453') in 0.020693 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through9.ogg
  artifactKey: Guid(d332d441eddd9f84e8d798f5f8736afa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through9.ogg using Guid(d332d441eddd9f84e8d798f5f8736afa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '04161a0bdbae87b3a6dc9c5dd4e198d2') in 0.0278409 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through2.ogg
  artifactKey: Guid(5bdd19a025d763f42903a897016a341b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through2.ogg using Guid(5bdd19a025d763f42903a897016a341b) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '13bad6f29b8b477fe4d06ba5dbdad062') in 0.0222018 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000147 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_walk8.ogg
  artifactKey: Guid(fbc516d668f35424ea41e186a2ad053f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_walk8.ogg using Guid(fbc516d668f35424ea41e186a2ad053f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '278de192623360675918874071b9cd53') in 0.0227191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_run10.ogg
  artifactKey: Guid(b0488861e6567c1449a1eca6cae16602) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_run10.ogg using Guid(b0488861e6567c1449a1eca6cae16602) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'be625a1f4f225ce4a41338c2abbfd637') in 0.0242437 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_walk6.ogg
  artifactKey: Guid(8aaffe40fc18ca744ac3b18d4e305fc0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_walk6.ogg using Guid(8aaffe40fc18ca744ac3b18d4e305fc0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8bd17572febc470e415a0124502d8487') in 0.0229585 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_run1.ogg
  artifactKey: Guid(e31b4045f97e8a3479ef9bc491ecaec2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_run1.ogg using Guid(e31b4045f97e8a3479ef9bc491ecaec2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '806d917ce1546565225de99f5120a80d') in 0.0236484 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_walk5.ogg
  artifactKey: Guid(e7db6cc82b55ab34ea4f968c946a85d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_walk5.ogg using Guid(e7db6cc82b55ab34ea4f968c946a85d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '409a485ce565e7603a6ce369201198eb') in 0.0200996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_walk7.ogg
  artifactKey: Guid(f0b5d9f99506408448eef1c402479aea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_walk7.ogg using Guid(f0b5d9f99506408448eef1c402479aea) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4513d05d0ec0916a919e30456a55dc26') in 0.0191831 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_walk4.ogg
  artifactKey: Guid(732fa80d3f18ba945839bb131db4d04d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_walk4.ogg using Guid(732fa80d3f18ba945839bb131db4d04d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '47bc3d036bc09b65a78061feb979d2b0') in 0.0242657 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/grass/grass_wander2.ogg
  artifactKey: Guid(72cf7b1a4d31f454780c015aeb8d85ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/grass/grass_wander2.ogg using Guid(72cf7b1a4d31f454780c015aeb8d85ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '05893394ddb87230e97eefffccffaab9') in 0.0168098 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_wander2.ogg
  artifactKey: Guid(608dad104ff4f80468d76d001297004a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_wander2.ogg using Guid(608dad104ff4f80468d76d001297004a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '85fe02e603a25acce28c77d2f04155fb') in 0.0307867 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/deckwood/deckwood_run6.ogg
  artifactKey: Guid(9c6b07d5efc2fed42b136caceff75b52) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/deckwood/deckwood_run6.ogg using Guid(9c6b07d5efc2fed42b136caceff75b52) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0c846dd204d3ef417057218549fc56a4') in 0.0292909 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/squeakywood/squeakywood_wander2.ogg
  artifactKey: Guid(530e10029c9ccf84e9681af1e9d50698) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/squeakywood/squeakywood_wander2.ogg using Guid(530e10029c9ccf84e9681af1e9d50698) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '12ac465927e5cd1f8740afe785bc2418') in 0.3842595 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/water/water_wander2.ogg
  artifactKey: Guid(8227d6f8c8f6cef4cb269924c0aa3905) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/water/water_wander2.ogg using Guid(8227d6f8c8f6cef4cb269924c0aa3905) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b81721dccf628f04684f6ad13df3bc34') in 0.1051107 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/chain/chain_5.ogg
  artifactKey: Guid(97d2a9c396c90cd40b9bfb1985439efd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/chain/chain_5.ogg using Guid(97d2a9c396c90cd40b9bfb1985439efd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '783f235ce3042261297e3a8e490552a4') in 0.0539318 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/chain/chain_6.ogg
  artifactKey: Guid(d2934da12c6525747bbefcb37c6207fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/chain/chain_6.ogg using Guid(d2934da12c6525747bbefcb37c6207fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c940daa9d22640a57380ed324843a524') in 0.0283263 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through8.ogg
  artifactKey: Guid(79eebaebf072a334f9ca1ef74ac98d81) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through8.ogg using Guid(79eebaebf072a334f9ca1ef74ac98d81) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '47c8aea5d9390628310c1a84fc92f008') in 0.0216974 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_walk7.ogg
  artifactKey: Guid(df5c656b9d9f0a542898643164d1d9b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_walk7.ogg using Guid(df5c656b9d9f0a542898643164d1d9b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aaad8134e496acfd939c9602c44612a6') in 0.0166125 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/water/water_wander2.ogg
  artifactKey: Guid(ba3d626f75824c54d82f30c236b94fec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/water/water_wander2.ogg using Guid(ba3d626f75824c54d82f30c236b94fec) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ea0b20eae0808b4e972c2b55fdb54fe7') in 0.0160062 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/grass/grass_walk3.ogg
  artifactKey: Guid(0e11505c1467d2548bab257125b5a3bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/grass/grass_walk3.ogg using Guid(0e11505c1467d2548bab257125b5a3bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '618d7448b1897126d0da8a3ccfd50453') in 0.0158773 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through5.ogg
  artifactKey: Guid(2b8bdbd0e5bb3c646a35e8376e7cf3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through5.ogg using Guid(2b8bdbd0e5bb3c646a35e8376e7cf3d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5799b4f9d82023cf71779408a7c9a96b') in 0.0158155 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/dirt/dirt_wander1.ogg
  artifactKey: Guid(20ae96ac6395734459e8b8107d14d3d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/dirt/dirt_wander1.ogg using Guid(20ae96ac6395734459e8b8107d14d3d6) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'def6d1e69548e536ff68e06b4dd99e94') in 0.024436 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_Game/Audio/Player/concrete/concrete_walk11.ogg
  artifactKey: Guid(014abfaa3a3141349ab0464e4c80db87) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Audio/Player/concrete/concrete_walk11.ogg using Guid(014abfaa3a3141349ab0464e4c80db87) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ef5c86ea482c3907536bc569499f10b') in 0.0166544 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/dirt/dirt_walk10.ogg
  artifactKey: Guid(4b7640e1930b30345aaf6c06664d17b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/dirt/dirt_walk10.ogg using Guid(4b7640e1930b30345aaf6c06664d17b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '913e42307ae05d5787b2f22e73231cab') in 0.0170176 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run10.ogg
  artifactKey: Guid(d95918856b86ea64c932bd32b3e685ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run10.ogg using Guid(d95918856b86ea64c932bd32b3e685ea) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b2e578b4b9018f2f1248f575208251a4') in 0.0211736 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run4.ogg
  artifactKey: Guid(ca8270df5846d72438abdd77599a97c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run4.ogg using Guid(ca8270df5846d72438abdd77599a97c8) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '990d631e7387b3dfa0100ac6c7057c02') in 0.0297943 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/squeakywood/squeakywood_wander2.ogg
  artifactKey: Guid(f30dc839040dc8d40b9074511bd9b546) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/squeakywood/squeakywood_wander2.ogg using Guid(f30dc839040dc8d40b9074511bd9b546) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5e835505b261ec1a1073feb4c37d0fdf') in 0.023818 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/metalbox/metalbox_wander3.ogg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/metalbox/metalbox_wander3.ogg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aaa5cff2762cb7476f9cb9fccea70750') in 0.0193775 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through7.ogg
  artifactKey: Guid(6898ba364cacf4b468faa6681e3f9b21) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through7.ogg using Guid(6898ba364cacf4b468faa6681e3f9b21) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a9b423f7fa3b0b401fc7bc27d68fc5e') in 0.0184078 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/metalbar/metalbar_wander3.ogg
  artifactKey: Guid(1bda798724d5fbc42b7b260e6f388e47) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/metalbar/metalbar_wander3.ogg using Guid(1bda798724d5fbc42b7b260e6f388e47) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f8d53f1849c8467a8a4e24e38b84ded3') in 0.0295392 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through11.ogg
  artifactKey: Guid(c58ffd65a4a6ba84785fe0eff58f5f75) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through11.ogg using Guid(c58ffd65a4a6ba84785fe0eff58f5f75) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1dba1321fa81433d493a540c137b1868') in 0.0231793 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run9.ogg
  artifactKey: Guid(98e47d515b7ee044ea9d6a97b72877b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run9.ogg using Guid(98e47d515b7ee044ea9d6a97b72877b4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '51eab8000f56b944ab7fc3130072686d') in 0.0248364 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000033 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_walk6.ogg
  artifactKey: Guid(2989ef5569ccb6a4b968144d67308468) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_walk6.ogg using Guid(2989ef5569ccb6a4b968144d67308468) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '299d3800818d646796d8bff90aa185f9') in 0.0177087 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run7.ogg
  artifactKey: Guid(0feebe13184b2464293a81d3f57bdd3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run7.ogg using Guid(0feebe13184b2464293a81d3f57bdd3f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ba8647064306f969390d58f320a006b1') in 0.0178005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_wander2.ogg
  artifactKey: Guid(ab9544ff4b5409a45ad610138ffb0862) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_wander2.ogg using Guid(ab9544ff4b5409a45ad610138ffb0862) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '16bf0add2675f9eebedb2ee486343c15') in 0.0106161 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/deckwood/deckwood_run11.ogg
  artifactKey: Guid(c1068da6aafd9b048b4a1eff68da9d21) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/deckwood/deckwood_run11.ogg using Guid(c1068da6aafd9b048b4a1eff68da9d21) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '37a18732061b8d44258d0a2558da9459') in 0.0163976 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/mud/mud_wander2.ogg
  artifactKey: Guid(c049b98ccfde567449b50549b00ac51f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/mud/mud_wander2.ogg using Guid(c049b98ccfde567449b50549b00ac51f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7299047d5f8254e44a4083bb4b8c59b9') in 0.0309978 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000617 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/mud/mud_wander3.ogg
  artifactKey: Guid(291fc57ea0b42d34c9bf7539d343b41f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/mud/mud_wander3.ogg using Guid(291fc57ea0b42d34c9bf7539d343b41f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3ce7ac4be57c34eff9d24a1703d41c8f') in 0.0331106 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through9.ogg
  artifactKey: Guid(12568b9ad3333974d8c3244fb34112be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through9.ogg using Guid(12568b9ad3333974d8c3244fb34112be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a166fd8548db6bc737912ef7b73a46d7') in 0.0180153 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/snow/snow_wander4.ogg
  artifactKey: Guid(79acd90bfda289e49907117d7076b6a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/snow/snow_wander4.ogg using Guid(79acd90bfda289e49907117d7076b6a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '131ef1b95b96079afdb51219644f31a6') in 0.018698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000061 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/marble/marble_wander1.ogg
  artifactKey: Guid(9c9105825dca3a8498816d6301a6b56d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/marble/marble_wander1.ogg using Guid(9c9105825dca3a8498816d6301a6b56d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '290065697edbafbac36b731526cae4ee') in 0.0281982 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run3.ogg
  artifactKey: Guid(d184ad668e0f20a459419ec2504341bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/deckwood/deckwood_run3.ogg using Guid(d184ad668e0f20a459419ec2504341bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '763a0c07bdc6399211f07802b53a815d') in 0.0168261 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_walk9.ogg
  artifactKey: Guid(aae8662316dd8b9459ad727928364f43) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_walk9.ogg using Guid(aae8662316dd8b9459ad727928364f43) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8a49f09e275df28733d1b3bf64ce0b28') in 0.0192029 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/chain/chain_10.ogg
  artifactKey: Guid(f8a07336090d64245ba8084e73f37453) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/chain/chain_10.ogg using Guid(f8a07336090d64245ba8084e73f37453) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6decacc9e19f7caf96dd7732a3e98e14') in 0.0180208 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/chain/chain_9.ogg
  artifactKey: Guid(692edc918cc567949922397c6c0e714f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/chain/chain_9.ogg using Guid(692edc918cc567949922397c6c0e714f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6a62da99e6ab79201adddda9e4376764') in 0.0202424 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000191 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/squeakywood/squeakywood_wander5.ogg
  artifactKey: Guid(32492c18fa9516e439b624c8cdee5db7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/squeakywood/squeakywood_wander5.ogg using Guid(32492c18fa9516e439b624c8cdee5db7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9071fce231a267a838fab6b6bf2cb4c6') in 0.0198448 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/water/water_wander3.ogg
  artifactKey: Guid(e17055200f5c2374098d98907f7465ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/water/water_wander3.ogg using Guid(e17055200f5c2374098d98907f7465ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f350ed463a3567c5465b21f9e1a4d3b0') in 0.0183487 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000016 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/grass/grass_wander3.ogg
  artifactKey: Guid(7d42cc53d5213174a9a55c1970b400f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/grass/grass_wander3.ogg using Guid(7d42cc53d5213174a9a55c1970b400f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aba11d0758b45227dd85806a128cda79') in 0.0152264 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/water_stereofix/water_wander1.ogg
  artifactKey: Guid(735e850ce1363514ca296d9cefa65468) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/water_stereofix/water_wander1.ogg using Guid(735e850ce1363514ca296d9cefa65468) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '153ba9795caf99adffbfab3c41a832df') in 0.0226032 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_walk10.ogg
  artifactKey: Guid(99ddf8c95c76d6242ac9d1520c7ca5fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_walk10.ogg using Guid(99ddf8c95c76d6242ac9d1520c7ca5fe) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f8aa2f96d14443bc020f4f5ca0c1c7a1') in 0.0157191 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/marble/marble_wander3.ogg
  artifactKey: Guid(08efd5ffd2e8fdd48ac080e0b266e18e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/marble/marble_wander3.ogg using Guid(08efd5ffd2e8fdd48ac080e0b266e18e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8cc3f7424cb68e6e40fcade5ddd802ba') in 0.0187521 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/dirt/dirt_walk6.ogg
  artifactKey: Guid(1bb731cebba591f4ea36be709763ad66) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/dirt/dirt_walk6.ogg using Guid(1bb731cebba591f4ea36be709763ad66) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '03fd429bc87b76604954b2006d48e4a6') in 0.0285794 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/marble/marble_wander6.ogg
  artifactKey: Guid(97c5a0f52fb6bcb4892ae1f2e73f15d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/marble/marble_wander6.ogg using Guid(97c5a0f52fb6bcb4892ae1f2e73f15d4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bcb851853f84a07509146a10c250193f') in 0.020132 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_wander4.ogg
  artifactKey: Guid(e60fe934b3a34b3469d140a9034670d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_wander4.ogg using Guid(e60fe934b3a34b3469d140a9034670d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa3c245a3090ef5356f783c336227eca') in 0.0194836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/chain/chain_8.ogg
  artifactKey: Guid(f2b4778feff62a54e9d2a0381f47490d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/chain/chain_8.ogg using Guid(f2b4778feff62a54e9d2a0381f47490d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9aa8a2a74c80a98270c9362c29378126') in 0.0818567 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/chain/chain_8.ogg
  artifactKey: Guid(82e7b83fb637e2b45991d160b9aeee2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/chain/chain_8.ogg using Guid(82e7b83fb637e2b45991d160b9aeee2f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1ba2107c0d5ceea7c907cc1221da7370') in 0.0322542 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_Game/Audio/Player/concrete/concrete_wander5.ogg
  artifactKey: Guid(e888741f19d349f4a99e2856bd615bfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Audio/Player/concrete/concrete_wander5.ogg using Guid(e888741f19d349f4a99e2856bd615bfd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'af6052cecd4e2e9f92278d6dee0132d7') in 0.0240314 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/metalbar/metalbar_wander5.ogg
  artifactKey: Guid(bf4050ebee0e6a9449e88e1aa2b85a0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/metalbar/metalbar_wander5.ogg using Guid(bf4050ebee0e6a9449e88e1aa2b85a0e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b53407cdd5fdc034c8d6c87f8078423c') in 0.0226664 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through7.ogg
  artifactKey: Guid(583dc3687ba7fdd4091d101973772765) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/brush/brush_through7.ogg using Guid(583dc3687ba7fdd4091d101973772765) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '77ae36c17d0abfbe5eb219710b1b2cac') in 0.0195746 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_Game/Audio/Player/concrete/concrete_run10.ogg
  artifactKey: Guid(488a5fe8567ae7a49985e22cbe6b6a0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Audio/Player/concrete/concrete_run10.ogg using Guid(488a5fe8567ae7a49985e22cbe6b6a0d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a0542c3d45f15d496a3379caa6e3cb5d') in 0.0195084 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_wander5.ogg
  artifactKey: Guid(5662cab8e8dccd548a78148623034aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_wander5.ogg using Guid(5662cab8e8dccd548a78148623034aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c6ad6627609a8f10c3f450b3c6e4206e') in 0.0176284 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000015 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_wander3.ogg
  artifactKey: Guid(6b16425ed40d5344f8cecb331e009928) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_wander3.ogg using Guid(6b16425ed40d5344f8cecb331e009928) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd324c80a3097e247c1ab2e12a3c89801') in 0.0224368 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/chain/chain_9.ogg
  artifactKey: Guid(0a3ba616b77822c41bad4c309cd0cd20) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/chain/chain_9.ogg using Guid(0a3ba616b77822c41bad4c309cd0cd20) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '98d294d7e0a98923fe837bf95ae52493') in 0.0175453 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/grass/grass_wander4.ogg
  artifactKey: Guid(4801bfaad9a51bc45b7ef1916bc4b940) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/grass/grass_wander4.ogg using Guid(4801bfaad9a51bc45b7ef1916bc4b940) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4820e42891656e26074aec9bf991eaed') in 0.0192766 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/grass/grass_wander1.ogg
  artifactKey: Guid(b1f5086ab9dd5684ca52b81a60fa5011) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/grass/grass_wander1.ogg using Guid(b1f5086ab9dd5684ca52b81a60fa5011) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '278e6bef2e546b138bf815f2d1aa9202') in 0.0219576 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/grass/grass_wander2.ogg
  artifactKey: Guid(d3d4740d9af22cb49a49fcdb73adf881) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/grass/grass_wander2.ogg using Guid(d3d4740d9af22cb49a49fcdb73adf881) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '446f680ed7d2c9a85038fbbfe724da96') in 0.0191079 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_wander5.ogg
  artifactKey: Guid(81cc5b26ce4febc47b8495b056d04c6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_wander5.ogg using Guid(81cc5b26ce4febc47b8495b056d04c6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0f15a9e6a3eb5976b4397378a367e2b7') in 0.0237059 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000018 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_wander3.ogg
  artifactKey: Guid(ae80f52ca9aeaad438d27d59eb9e1c10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/dirt/dirt_wander3.ogg using Guid(ae80f52ca9aeaad438d27d59eb9e1c10) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '34ee24c022b145cf443d71602b2ae664') in 0.0200658 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/gravel/gravel_wander3.ogg
  artifactKey: Guid(628e91908f2946f429c568f8161eb2c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/gravel/gravel_wander3.ogg using Guid(628e91908f2946f429c568f8161eb2c9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5f99fe21be2e46f669cf995b91ac1c8a') in 0.0286572 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/squeakywood/squeakywood_wander3.ogg
  artifactKey: Guid(3b3450fdea6c831428612219d2a0fab2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/squeakywood/squeakywood_wander3.ogg using Guid(3b3450fdea6c831428612219d2a0fab2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c57c9b40bc6898a783b3dd07c3b6cbc1') in 0.015623 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/mud/mud_wander2.ogg
  artifactKey: Guid(812284aa99fe05443a7c0a69968ce0ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/mud/mud_wander2.ogg using Guid(812284aa99fe05443a7c0a69968ce0ee) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ccfb5f988907e6017a008a796e6c8605') in 0.0164135 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/stone/stone_wander4.ogg
  artifactKey: Guid(fa6b1c0a72301c54a96d187d6017c7f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/stone/stone_wander4.ogg using Guid(fa6b1c0a72301c54a96d187d6017c7f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '6b6bc3c50dbf27c71f50ab987d8f9dd4') in 0.018205 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/metalbox/metalbox_wander2.ogg
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/metalbox/metalbox_wander2.ogg using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '9d82e87410d4f6edc5a1fd49c8112541') in 0.0154458 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_Game/Audio/Player/concrete/concrete_run2.ogg
  artifactKey: Guid(e3c05fce640699d419f03a9f02da8b05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Audio/Player/concrete/concrete_run2.ogg using Guid(e3c05fce640699d419f03a9f02da8b05) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e78c6412a2d6b5b4dac87c5a9ba8fafb') in 0.0174211 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_wander1.ogg
  artifactKey: Guid(407e541b113952144bb19505a5b84bba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_wander1.ogg using Guid(407e541b113952144bb19505a5b84bba) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '285d5b217ba09bf181922d2c7446c825') in 0.0149903 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_run6.ogg
  artifactKey: Guid(a36423f2ea20fc84eb4f0a7b2324c354) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_run6.ogg using Guid(a36423f2ea20fc84eb4f0a7b2324c354) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '196479789facc7fefe1b4d26e89f4847') in 0.0202844 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_walk2.ogg
  artifactKey: Guid(5acaea48aea580f46b397832041a2277) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_walk2.ogg using Guid(5acaea48aea580f46b397832041a2277) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '53d79a4552f47e1761e98938b95fe906') in 0.0209489 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000020 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/chain/chain_5.ogg
  artifactKey: Guid(413ce30692b36c547adfdc4341619fb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/chain/chain_5.ogg using Guid(413ce30692b36c547adfdc4341619fb2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '5df3cc3078e5a44f26f23dd3ff7fd0be') in 0.0160642 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_walk11.ogg
  artifactKey: Guid(2ea94e331ead52b44942ee3598b939a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/bluntwood/bluntwood_walk11.ogg using Guid(2ea94e331ead52b44942ee3598b939a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '24f609f3490b54ab997be1b974a8f77f') in 0.0159835 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/grass/grass_wander6.ogg
  artifactKey: Guid(16a75b1f89e1b7643a910ac52947f0d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/grass/grass_wander6.ogg using Guid(16a75b1f89e1b7643a910ac52947f0d2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '14c4eeb9739d9b4505d98d3cdcb54e78') in 0.0155529 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/snow/snow_wander5.ogg
  artifactKey: Guid(a37ff86fa55bb97408247b4f62095fe7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/snow/snow_wander5.ogg using Guid(a37ff86fa55bb97408247b4f62095fe7) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f660a9bc14ec75b77e1b66d2c943e3a4') in 0.0143378 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000019 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_wander2.ogg
  artifactKey: Guid(d029c8126f88ec343a5d739aa06e99a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/bluntwood/bluntwood_wander2.ogg using Guid(d029c8126f88ec343a5d739aa06e99a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '257120a0cf6c674eb51465b60a656765') in 0.0178515 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_run10.ogg
  artifactKey: Guid(3f64a713ef6caef42950e0b1c748415d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_run10.ogg using Guid(3f64a713ef6caef42950e0b1c748415d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb28561582cc1145592b67fab10d8e91') in 0.0079231 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_run4.ogg
  artifactKey: Guid(bfc8fad6233be454ab7db87c9b858282) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_run4.ogg using Guid(bfc8fad6233be454ab7db87c9b858282) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d38a6c35f2e4b971a225a718f76c305') in 0.0077345 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/deckwood/deckwood_run8.ogg
  artifactKey: Guid(2cd9de94b9ecb4046b0f4c0964c6f0be) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/deckwood/deckwood_run8.ogg using Guid(2cd9de94b9ecb4046b0f4c0964c6f0be) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bea8b4982f3ff01f44f7d549cce02a4f') in 0.0218503 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000017 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through10.ogg
  artifactKey: Guid(dd79106e30912734a94e7f13cfcd3726) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through10.ogg using Guid(dd79106e30912734a94e7f13cfcd3726) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f5b8371052b8c2a046ed48a7cb158445') in 0.0194615 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_walk8.ogg
  artifactKey: Guid(24e241caab6d43647abef05b82e40b49) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/concrete/concrete_walk8.ogg using Guid(24e241caab6d43647abef05b82e40b49) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '15b707444606e243037b34b14970f120') in 0.0159916 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000022 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through4.ogg
  artifactKey: Guid(a019945cfbc88a94a985d2097df13f16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/brush/brush_through4.ogg using Guid(a019945cfbc88a94a985d2097df13f16) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7597d7ad5566e44ff832eb43c805870c') in 0.0299541 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_run8.ogg
  artifactKey: Guid(0722a84a7d71b6a4e8395557f0b0e656) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootsteps/sounds/concrete/concrete_run8.ogg using Guid(0722a84a7d71b6a4e8395557f0b0e656) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '912fc0528afe92e1ab289c3709429b5c') in 0.0084444 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.000021 seconds.
  path: Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/marble/marble_wander7.ogg
  artifactKey: Guid(566a5f3fbbcc0be4bbfa6da1d13e260d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_MCmodForCodePurposes/src/main/resources/assets/presencefootstepsmono/sounds/marble/marble_wander7.ogg using Guid(566a5f3fbbcc0be4bbfa6da1d13e260d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0a4d5ac3a08032a72967af9654adbf69') in 0.0167038 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 0.026736 seconds.
  path: Assets/_Game/Resources/Audio/Events/Walk 1.asset
  artifactKey: Guid(24aae328cce3ade44be3455b92d02be0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Audio/Events/Walk 1.asset using Guid(24aae328cce3ade44be3455b92d02be0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '01c6b9ddf9acbe91cee09c03aa111898') in 0.0058986 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 2.933966 seconds.
  path: Assets/_Game/Resources/Audio/Events/Wander.asset
  artifactKey: Guid(24aae328cce3ade44be3455b92d02be0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Audio/Events/Wander.asset using Guid(24aae328cce3ade44be3455b92d02be0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8c736655eb8bf6c17e3c84f37d4985a2') in 0.005362 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 39.537790 seconds.
  path: Assets/_Game/Resources/Audio/Events/Walk.asset
  artifactKey: Guid(7b7271b41ecc926458d104dc3a62fbf1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Audio/Events/Walk.asset using Guid(7b7271b41ecc926458d104dc3a62fbf1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '205d9e3ef73d7e5bfc0eab38a4732b57') in 0.0130928 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 390.005340 seconds.
  path: Assets/_Game/Scenes/Main.unity
  artifactKey: Guid(80207517658dd27438d88dd267d39966) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scenes/Main.unity using Guid(80207517658dd27438d88dd267d39966) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4c80899fa9d463bbae2340e96a5b155e') in 0.0014996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.285 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 58.15 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.469 seconds
Domain Reload Profiling: 3729ms
	BeginReloadAssembly (866ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (157ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (97ms)
	RebuildCommonClasses (81ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (80ms)
	LoadAllAssembliesAndSetupDomain (1211ms)
		LoadAssemblies (1179ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (422ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (376ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1469ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1089ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (287ms)
			ProcessInitializeOnLoadAttributes (602ms)
			ProcessInitializeOnLoadMethodAttributes (164ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (46ms)
Refreshing native plugins compatible for Editor in 95.17 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7985 unused Assets / (4.4 MB). Loaded Objects now: 9021.
Memory consumption went from 318.4 MB to 314.0 MB.
Total: 27.004500 ms (FindLiveObjects: 1.298100 ms CreateObjectMapping: 3.991100 ms MarkObjects: 13.865300 ms  DeleteObjects: 7.848100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 738.314105 seconds.
  path: Assets/_Game/Resources/Audio/Events/Walk 1.asset
  artifactKey: Guid(0273dadb28bee0b48b694e741ec50d9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Audio/Events/Walk 1.asset using Guid(0273dadb28bee0b48b694e741ec50d9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c644938dbc57c4918ce2ab0bb8cc1944') in 0.0691475 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 4.380713 seconds.
  path: Assets/_Game/Resources/Audio/Events/Sprint.asset
  artifactKey: Guid(0273dadb28bee0b48b694e741ec50d9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Audio/Events/Sprint.asset using Guid(0273dadb28bee0b48b694e741ec50d9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '257f9ca23c3de0fe689e3b3c60039842') in 0.009948 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 39.670504 seconds.
  path: Assets/_Game/Resources/Audio/Events/Sprint.asset
  artifactKey: Guid(0273dadb28bee0b48b694e741ec50d9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Audio/Events/Sprint.asset using Guid(0273dadb28bee0b48b694e741ec50d9a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cac880eb10893b5dcf847c7573fab21b') in 0.005847 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.904 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 51.75 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.320 seconds
Domain Reload Profiling: 3188ms
	BeginReloadAssembly (693ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (78ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (84ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (66ms)
	LoadAllAssembliesAndSetupDomain (1043ms)
		LoadAssemblies (893ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (351ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (314ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1321ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (966ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (258ms)
			ProcessInitializeOnLoadAttributes (537ms)
			ProcessInitializeOnLoadMethodAttributes (138ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 237.98 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7985 unused Assets / (2.9 MB). Loaded Objects now: 9023.
Memory consumption went from 318.4 MB to 315.6 MB.
Total: 75.491500 ms (FindLiveObjects: 1.909700 ms CreateObjectMapping: 3.242300 ms MarkObjects: 66.647500 ms  DeleteObjects: 3.690200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.027 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 75.32 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.270 seconds
Domain Reload Profiling: 3274ms
	BeginReloadAssembly (703ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (48ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (88ms)
	RebuildNativeTypeToScriptingClass (26ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (1108ms)
		LoadAssemblies (1233ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (379ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (339ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1271ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (951ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (274ms)
			ProcessInitializeOnLoadAttributes (516ms)
			ProcessInitializeOnLoadMethodAttributes (128ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 411.44 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7985 unused Assets / (2.9 MB). Loaded Objects now: 9025.
Memory consumption went from 318.4 MB to 315.5 MB.
Total: 69.981300 ms (FindLiveObjects: 44.891200 ms CreateObjectMapping: 5.489300 ms MarkObjects: 15.062900 ms  DeleteObjects: 4.536000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.598 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 58.99 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.260 seconds
Domain Reload Profiling: 2827ms
	BeginReloadAssembly (384ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (49ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (73ms)
	LoadAllAssembliesAndSetupDomain (1042ms)
		LoadAssemblies (876ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (338ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (296ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1261ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (947ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (259ms)
			ProcessInitializeOnLoadAttributes (524ms)
			ProcessInitializeOnLoadMethodAttributes (131ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 888.63 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7985 unused Assets / (5.9 MB). Loaded Objects now: 9027.
Memory consumption went from 318.4 MB to 312.5 MB.
Total: 99.647700 ms (FindLiveObjects: 1.381100 ms CreateObjectMapping: 2.752200 ms MarkObjects: 18.306200 ms  DeleteObjects: 77.206800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.235 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 60.23 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.738 seconds
Domain Reload Profiling: 3953ms
	BeginReloadAssembly (441ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (44ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (95ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (60ms)
	LoadAllAssembliesAndSetupDomain (1644ms)
		LoadAssemblies (1346ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (489ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (456ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1739ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1307ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (411ms)
			ProcessInitializeOnLoadAttributes (588ms)
			ProcessInitializeOnLoadMethodAttributes (269ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 187.36 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7985 unused Assets / (7.5 MB). Loaded Objects now: 9029.
Memory consumption went from 318.4 MB to 310.9 MB.
Total: 5046.849200 ms (FindLiveObjects: 1127.719200 ms CreateObjectMapping: 2965.276200 ms MarkObjects: 664.838600 ms  DeleteObjects: 289.013400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.941 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 51.20 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.331 seconds
Domain Reload Profiling: 3167ms
	BeginReloadAssembly (441ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (55ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (1238ms)
		LoadAssemblies (1088ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (336ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (299ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1332ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1003ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (281ms)
			ProcessInitializeOnLoadAttributes (549ms)
			ProcessInitializeOnLoadMethodAttributes (140ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 83.89 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7985 unused Assets / (2.9 MB). Loaded Objects now: 9031.
Memory consumption went from 318.5 MB to 315.6 MB.
Total: 31.899700 ms (FindLiveObjects: 1.789800 ms CreateObjectMapping: 1.738000 ms MarkObjects: 21.631000 ms  DeleteObjects: 6.739300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.300 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 54.75 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.571 seconds
Domain Reload Profiling: 3846ms
	BeginReloadAssembly (747ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (208ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (68ms)
	RebuildCommonClasses (84ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (72ms)
	LoadAllAssembliesAndSetupDomain (1349ms)
		LoadAssemblies (937ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (609ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (566ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1572ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1180ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (315ms)
			ProcessInitializeOnLoadAttributes (622ms)
			ProcessInitializeOnLoadMethodAttributes (207ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 140.00 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7985 unused Assets / (4.8 MB). Loaded Objects now: 9033.
Memory consumption went from 318.5 MB to 313.6 MB.
Total: 26.780400 ms (FindLiveObjects: 1.443900 ms CreateObjectMapping: 3.427600 ms MarkObjects: 15.524800 ms  DeleteObjects: 6.381900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 55.13 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 9033.
Memory consumption went from 317.7 MB to 314.8 MB.
Total: 451.491100 ms (FindLiveObjects: 1.245400 ms CreateObjectMapping: 4.127600 ms MarkObjects: 235.552400 ms  DeleteObjects: 210.563900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 5643.061327 seconds.
  path: Assets/_Game/Audio/concrete
  artifactKey: Guid(d7d34757125a4c24e87eb5394c963beb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Audio/concrete using Guid(d7d34757125a4c24e87eb5394c963beb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7004db0e494844d26c2bffc41bdaede2') in 0.4171715 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 35.796519 seconds.
  path: Assets/_Game/Scripts/Interface/BatteryController.cs
  artifactKey: Guid(7cee18eeccaa4d54cace132328fe131e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/BatteryController.cs using Guid(7cee18eeccaa4d54cace132328fe131e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '41036bc425a778ab646359d2ad78b1f5') in 0.0029298 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.153777 seconds.
  path: Assets/_Game/Scripts/Interface/NotificationManager.cs
  artifactKey: Guid(7ef52bbe62954894c86835b3d8fb644e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/NotificationManager.cs using Guid(7ef52bbe62954894c86835b3d8fb644e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '85f3f2376155b293df2af093fb3118bd') in 0.0022113 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.530133 seconds.
  path: Assets/_Game/Scripts/Interface/Resources/alert.uss
  artifactKey: Guid(1ce1f1ca75e43684095bda95dc431517) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Interface/Resources/alert.uss using Guid(1ce1f1ca75e43684095bda95dc431517) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Unknown pseudo class "last-child"
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:LogFormat (UnityEngine.LogType,string,object[])
UnityEngine.Debug:LogWarningFormat (string,object[])
UnityEngine.UIElements.StyleComplexSelector:CachePseudoStateMasks ()
UnityEngine.UIElements.StyleSheet:SetupReferences ()
UnityEngine.UIElements.StyleSheet:OnEnable ()

 -> (artifact id: '7b13ae81bab3587d0c6ce00ccfe638c0') in 0.0247758 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.786 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 29.92 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  6.087 seconds
Domain Reload Profiling: 9825ms
	BeginReloadAssembly (1679ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (122ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (286ms)
	RebuildCommonClasses (124ms)
	RebuildNativeTypeToScriptingClass (33ms)
	initialDomainReloadingComplete (136ms)
	LoadAllAssembliesAndSetupDomain (1767ms)
		LoadAssemblies (2169ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (670ms)
			TypeCache.Refresh (36ms)
				TypeCache.ScanAssembly (8ms)
			BuildScriptInfoCaches (605ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (6088ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (5669ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (2821ms)
			ProcessInitializeOnLoadAttributes (2667ms)
			ProcessInitializeOnLoadMethodAttributes (151ms)
			AfterProcessingInitializeOnLoad (20ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (38ms)
Refreshing native plugins compatible for Editor in 53.18 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9035.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 22.487300 ms (FindLiveObjects: 2.535600 ms CreateObjectMapping: 1.199000 ms MarkObjects: 11.903100 ms  DeleteObjects: 6.847900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.722 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 30.80 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.293 seconds
Domain Reload Profiling: 2987ms
	BeginReloadAssembly (509ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (57ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (119ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (1051ms)
		LoadAssemblies (925ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (328ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (288ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1293ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (984ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (313ms)
			ProcessInitializeOnLoadAttributes (507ms)
			ProcessInitializeOnLoadMethodAttributes (135ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 72.67 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9037.
Memory consumption went from 318.0 MB to 315.2 MB.
Total: 18.824200 ms (FindLiveObjects: 1.345000 ms CreateObjectMapping: 2.318700 ms MarkObjects: 11.701600 ms  DeleteObjects: 3.457500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.399 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 22.29 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.105 seconds
Domain Reload Profiling: 2484ms
	BeginReloadAssembly (388ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (49ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (38ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (45ms)
	LoadAllAssembliesAndSetupDomain (891ms)
		LoadAssemblies (758ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (307ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (272ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1106ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (839ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (220ms)
			ProcessInitializeOnLoadAttributes (430ms)
			ProcessInitializeOnLoadMethodAttributes (159ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (36ms)
Refreshing native plugins compatible for Editor in 38.02 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9039.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 13.933500 ms (FindLiveObjects: 1.571700 ms CreateObjectMapping: 0.823400 ms MarkObjects: 8.416900 ms  DeleteObjects: 3.119900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.793 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 29.27 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.332 seconds
Domain Reload Profiling: 3070ms
	BeginReloadAssembly (473ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (65ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (61ms)
	LoadAllAssembliesAndSetupDomain (1135ms)
		LoadAssemblies (994ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (347ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (311ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1333ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1016ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (272ms)
			ProcessInitializeOnLoadAttributes (566ms)
			ProcessInitializeOnLoadMethodAttributes (144ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 52.27 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (4.3 MB). Loaded Objects now: 9041.
Memory consumption went from 318.1 MB to 313.7 MB.
Total: 17.427000 ms (FindLiveObjects: 1.550300 ms CreateObjectMapping: 1.036200 ms MarkObjects: 11.335600 ms  DeleteObjects: 3.503400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.905 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 31.33 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.312 seconds
Domain Reload Profiling: 3198ms
	BeginReloadAssembly (474ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (101ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (1288ms)
		LoadAssemblies (1041ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (421ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (369ms)
			ResolveRequiredComponents (26ms)
	FinalizeReload (1313ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (980ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (293ms)
			ProcessInitializeOnLoadAttributes (519ms)
			ProcessInitializeOnLoadMethodAttributes (134ms)
			AfterProcessingInitializeOnLoad (24ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (35ms)
Refreshing native plugins compatible for Editor in 71.90 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.5 MB). Loaded Objects now: 9043.
Memory consumption went from 318.1 MB to 314.5 MB.
Total: 29.719400 ms (FindLiveObjects: 3.882000 ms CreateObjectMapping: 1.546500 ms MarkObjects: 14.611500 ms  DeleteObjects: 9.677700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.103 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 38.72 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.080 seconds
Domain Reload Profiling: 4139ms
	BeginReloadAssembly (590ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (155ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (80ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (165ms)
	LoadAllAssembliesAndSetupDomain (1211ms)
		LoadAssemblies (1063ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (373ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (330ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (2081ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1538ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (382ms)
			ProcessInitializeOnLoadAttributes (901ms)
			ProcessInitializeOnLoadMethodAttributes (203ms)
			AfterProcessingInitializeOnLoad (38ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (55ms)
Refreshing native plugins compatible for Editor in 43.08 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (5.9 MB). Loaded Objects now: 9045.
Memory consumption went from 318.1 MB to 312.2 MB.
Total: 20.216400 ms (FindLiveObjects: 1.222100 ms CreateObjectMapping: 1.189400 ms MarkObjects: 13.223500 ms  DeleteObjects: 4.579700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.834 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 36.72 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.418 seconds
Domain Reload Profiling: 3220ms
	BeginReloadAssembly (492ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (61ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (103ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (100ms)
	LoadAllAssembliesAndSetupDomain (1087ms)
		LoadAssemblies (930ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (379ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (334ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1418ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1093ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (314ms)
			ProcessInitializeOnLoadAttributes (600ms)
			ProcessInitializeOnLoadMethodAttributes (144ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (36ms)
Refreshing native plugins compatible for Editor in 126.61 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9047.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 24.914800 ms (FindLiveObjects: 1.221800 ms CreateObjectMapping: 2.834500 ms MarkObjects: 16.611500 ms  DeleteObjects: 4.245600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4796.150448 seconds.
  path: Assets/_Game/Resources/Audio/Events/FootSlide.asset
  artifactKey: Guid(24aae328cce3ade44be3455b92d02be0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Audio/Events/FootSlide.asset using Guid(24aae328cce3ade44be3455b92d02be0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '97e3e459193f03c12fce8671263e96bc') in 0.5738366 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.410 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.19 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.160 seconds
Domain Reload Profiling: 2552ms
	BeginReloadAssembly (370ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (39ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (914ms)
		LoadAssemblies (779ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (300ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (263ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1161ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (855ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (228ms)
			ProcessInitializeOnLoadAttributes (473ms)
			ProcessInitializeOnLoadMethodAttributes (124ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 132.56 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9049.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 23.063900 ms (FindLiveObjects: 2.285100 ms CreateObjectMapping: 1.803600 ms MarkObjects: 14.733000 ms  DeleteObjects: 4.240600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.675 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 36.40 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.537 seconds
Domain Reload Profiling: 3186ms
	BeginReloadAssembly (446ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (91ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (75ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (1053ms)
		LoadAssemblies (861ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (366ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (324ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1538ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1132ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (257ms)
			ProcessInitializeOnLoadAttributes (625ms)
			ProcessInitializeOnLoadMethodAttributes (208ms)
			AfterProcessingInitializeOnLoad (30ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (43ms)
Refreshing native plugins compatible for Editor in 53.24 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9051.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 25.454100 ms (FindLiveObjects: 1.236200 ms CreateObjectMapping: 1.383300 ms MarkObjects: 9.812200 ms  DeleteObjects: 13.020900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.244 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 67.76 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.518 seconds
Domain Reload Profiling: 4698ms
	BeginReloadAssembly (1335ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (119ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (89ms)
	RebuildCommonClasses (81ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (97ms)
	LoadAllAssembliesAndSetupDomain (1642ms)
		LoadAssemblies (2234ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (403ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (360ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1518ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1081ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (263ms)
			ProcessInitializeOnLoadAttributes (623ms)
			ProcessInitializeOnLoadMethodAttributes (161ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 287.14 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9053.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 101.895100 ms (FindLiveObjects: 2.047300 ms CreateObjectMapping: 2.721900 ms MarkObjects: 75.040600 ms  DeleteObjects: 22.083500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 44.19 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (5.0 MB). Loaded Objects now: 9053.
Memory consumption went from 317.8 MB to 312.8 MB.
Total: 17.616100 ms (FindLiveObjects: 1.526000 ms CreateObjectMapping: 1.262900 ms MarkObjects: 9.108800 ms  DeleteObjects: 5.716900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  4.478 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 36.91 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.611 seconds
Domain Reload Profiling: 6059ms
	BeginReloadAssembly (1730ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (158ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (602ms)
	RebuildCommonClasses (288ms)
	RebuildNativeTypeToScriptingClass (29ms)
	initialDomainReloadingComplete (104ms)
	LoadAllAssembliesAndSetupDomain (2298ms)
		LoadAssemblies (2557ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (523ms)
			TypeCache.Refresh (31ms)
				TypeCache.ScanAssembly (17ms)
			BuildScriptInfoCaches (460ms)
			ResolveRequiredComponents (24ms)
	FinalizeReload (1612ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1189ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (313ms)
			ProcessInitializeOnLoadAttributes (662ms)
			ProcessInitializeOnLoadMethodAttributes (175ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (47ms)
Refreshing native plugins compatible for Editor in 52.33 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9055.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 27.544500 ms (FindLiveObjects: 1.524900 ms CreateObjectMapping: 1.792200 ms MarkObjects: 20.413000 ms  DeleteObjects: 3.812700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.365 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 39.86 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  3.323 seconds
Domain Reload Profiling: 5662ms
	BeginReloadAssembly (513ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (68ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (95ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (1643ms)
		LoadAssemblies (1315ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (583ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (543ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (3324ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2896ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (11ms)
			BeforeProcessingInitializeOnLoad (1932ms)
			ProcessInitializeOnLoadAttributes (758ms)
			ProcessInitializeOnLoadMethodAttributes (155ms)
			AfterProcessingInitializeOnLoad (35ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 77.45 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (9.1 MB). Loaded Objects now: 9057.
Memory consumption went from 318.1 MB to 309.0 MB.
Total: 115.333500 ms (FindLiveObjects: 1.220700 ms CreateObjectMapping: 0.819500 ms MarkObjects: 69.276700 ms  DeleteObjects: 44.015100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.207 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 30.34 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.364 seconds
Domain Reload Profiling: 3534ms
	BeginReloadAssembly (1007ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (72ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (248ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (76ms)
	LoadAllAssembliesAndSetupDomain (1001ms)
		LoadAssemblies (1200ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (324ms)
			TypeCache.Refresh (19ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (284ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1364ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1034ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (284ms)
			ProcessInitializeOnLoadAttributes (576ms)
			ProcessInitializeOnLoadMethodAttributes (136ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 99.02 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9059.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 25.166700 ms (FindLiveObjects: 1.648800 ms CreateObjectMapping: 1.740100 ms MarkObjects: 14.573600 ms  DeleteObjects: 7.202900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.631 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 29.10 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.264 seconds
Domain Reload Profiling: 2864ms
	BeginReloadAssembly (369ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (42ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (73ms)
	LoadAllAssembliesAndSetupDomain (1097ms)
		LoadAssemblies (930ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (337ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (300ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1264ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (934ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (240ms)
			ProcessInitializeOnLoadAttributes (528ms)
			ProcessInitializeOnLoadMethodAttributes (135ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 41.42 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9061.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 19.307400 ms (FindLiveObjects: 2.172700 ms CreateObjectMapping: 1.113700 ms MarkObjects: 12.574400 ms  DeleteObjects: 3.445200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.603 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 31.78 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.314 seconds
Domain Reload Profiling: 2883ms
	BeginReloadAssembly (367ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (1056ms)
		LoadAssemblies (882ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (345ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (309ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1314ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1001ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (275ms)
			ProcessInitializeOnLoadAttributes (536ms)
			ProcessInitializeOnLoadMethodAttributes (154ms)
			AfterProcessingInitializeOnLoad (27ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (39ms)
Refreshing native plugins compatible for Editor in 75.59 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9063.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 51.399500 ms (FindLiveObjects: 2.601100 ms CreateObjectMapping: 1.084500 ms MarkObjects: 42.508900 ms  DeleteObjects: 5.203800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.736 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 38.23 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.497 seconds
Domain Reload Profiling: 3193ms
	BeginReloadAssembly (401ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (34ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (82ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (110ms)
	LoadAllAssembliesAndSetupDomain (1083ms)
		LoadAssemblies (942ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (349ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (312ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1498ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1123ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (364ms)
			ProcessInitializeOnLoadAttributes (535ms)
			ProcessInitializeOnLoadMethodAttributes (181ms)
			AfterProcessingInitializeOnLoad (34ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 45.30 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.1 MB). Loaded Objects now: 9065.
Memory consumption went from 318.1 MB to 315.1 MB.
Total: 16.183700 ms (FindLiveObjects: 1.285400 ms CreateObjectMapping: 1.707200 ms MarkObjects: 10.036300 ms  DeleteObjects: 3.152800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.894 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 95.14 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.341 seconds
Domain Reload Profiling: 3200ms
	BeginReloadAssembly (540ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (31ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (87ms)
	RebuildCommonClasses (78ms)
	RebuildNativeTypeToScriptingClass (72ms)
	initialDomainReloadingComplete (89ms)
	LoadAllAssembliesAndSetupDomain (1079ms)
		LoadAssemblies (1017ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (362ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (322ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1341ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1016ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (292ms)
			ProcessInitializeOnLoadAttributes (570ms)
			ProcessInitializeOnLoadMethodAttributes (126ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (36ms)
Refreshing native plugins compatible for Editor in 40.04 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (4.7 MB). Loaded Objects now: 9067.
Memory consumption went from 318.1 MB to 313.4 MB.
Total: 13.682300 ms (FindLiveObjects: 1.340600 ms CreateObjectMapping: 0.978200 ms MarkObjects: 7.801300 ms  DeleteObjects: 3.561100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.422 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 27.85 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.172 seconds
Domain Reload Profiling: 2564ms
	BeginReloadAssembly (358ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (67ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (57ms)
	LoadAllAssembliesAndSetupDomain (921ms)
		LoadAssemblies (775ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (299ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (263ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1173ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (894ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (235ms)
			ProcessInitializeOnLoadAttributes (507ms)
			ProcessInitializeOnLoadMethodAttributes (122ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 48.14 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9069.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 21.782700 ms (FindLiveObjects: 2.040000 ms CreateObjectMapping: 1.188900 ms MarkObjects: 14.301200 ms  DeleteObjects: 4.250700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 34.54 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 9069.
Memory consumption went from 317.8 MB to 314.9 MB.
Total: 14.362400 ms (FindLiveObjects: 1.224000 ms CreateObjectMapping: 0.916900 ms MarkObjects: 8.349900 ms  DeleteObjects: 3.870400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.501 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 27.60 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.194 seconds
Domain Reload Profiling: 2671ms
	BeginReloadAssembly (502ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (61ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (156ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (51ms)
	LoadAllAssembliesAndSetupDomain (865ms)
		LoadAssemblies (760ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (303ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (271ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1194ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (899ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (251ms)
			ProcessInitializeOnLoadAttributes (500ms)
			ProcessInitializeOnLoadMethodAttributes (122ms)
			AfterProcessingInitializeOnLoad (19ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 88.53 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (6.1 MB). Loaded Objects now: 9071.
Memory consumption went from 318.1 MB to 312.0 MB.
Total: 20.541800 ms (FindLiveObjects: 1.491500 ms CreateObjectMapping: 1.017100 ms MarkObjects: 10.149100 ms  DeleteObjects: 7.882600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.536 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 31.04 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.128 seconds
Domain Reload Profiling: 2608ms
	BeginReloadAssembly (355ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (42ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (52ms)
	RebuildNativeTypeToScriptingClass (95ms)
	initialDomainReloadingComplete (70ms)
	LoadAllAssembliesAndSetupDomain (907ms)
		LoadAssemblies (776ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (288ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (258ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1129ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (868ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (1ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (221ms)
			ProcessInitializeOnLoadAttributes (475ms)
			ProcessInitializeOnLoadMethodAttributes (140ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (36ms)
Refreshing native plugins compatible for Editor in 46.85 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.1 MB). Loaded Objects now: 9073.
Memory consumption went from 318.1 MB to 315.0 MB.
Total: 17.304900 ms (FindLiveObjects: 1.400100 ms CreateObjectMapping: 1.464900 ms MarkObjects: 10.557100 ms  DeleteObjects: 3.880900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.499 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 27.46 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.275 seconds
Domain Reload Profiling: 2746ms
	BeginReloadAssembly (404ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (64ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (57ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (925ms)
		LoadAssemblies (794ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (324ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (290ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1276ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (974ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (268ms)
			ProcessInitializeOnLoadAttributes (529ms)
			ProcessInitializeOnLoadMethodAttributes (144ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (37ms)
Refreshing native plugins compatible for Editor in 39.53 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (8.3 MB). Loaded Objects now: 9075.
Memory consumption went from 318.1 MB to 309.8 MB.
Total: 25.214800 ms (FindLiveObjects: 1.442100 ms CreateObjectMapping: 1.250400 ms MarkObjects: 10.895000 ms  DeleteObjects: 11.625500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.460 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 31.62 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.443 seconds
Domain Reload Profiling: 2879ms
	BeginReloadAssembly (349ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (52ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (71ms)
	LoadAllAssembliesAndSetupDomain (945ms)
		LoadAssemblies (766ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (337ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (303ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1443ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1094ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (343ms)
			ProcessInitializeOnLoadAttributes (565ms)
			ProcessInitializeOnLoadMethodAttributes (148ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 61.15 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9077.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 15.479900 ms (FindLiveObjects: 1.905900 ms CreateObjectMapping: 1.605100 ms MarkObjects: 8.333000 ms  DeleteObjects: 3.634200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 5250.561048 seconds.
  path: Assets/_Game/Scripts/Inv/InvDragAndDropManager.cs
  artifactKey: Guid(1cbe1220b197af04781436e4f1728414) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Inv/InvDragAndDropManager.cs using Guid(1cbe1220b197af04781436e4f1728414) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '28ba64cddf714d4b8e415ca3e9c2df90') in 0.796005 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.486527 seconds.
  path: Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs
  artifactKey: Guid(5a6f5efa8c41a8f40a6dbe368ef9fa7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs using Guid(5a6f5efa8c41a8f40a6dbe368ef9fa7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7f50d6e4a2845894baa74ce71e11dd74') in 0.0028836 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  3.911 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.03 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.099 seconds
Domain Reload Profiling: 4980ms
	BeginReloadAssembly (720ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (74ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (105ms)
	RebuildCommonClasses (156ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (2929ms)
		LoadAssemblies (2756ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (602ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (3ms)
			BuildScriptInfoCaches (571ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1099ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (828ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (227ms)
			ProcessInitializeOnLoadAttributes (440ms)
			ProcessInitializeOnLoadMethodAttributes (132ms)
			AfterProcessingInitializeOnLoad (22ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 77.75 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (4.7 MB). Loaded Objects now: 9079.
Memory consumption went from 318.2 MB to 313.5 MB.
Total: 201.755900 ms (FindLiveObjects: 3.253500 ms CreateObjectMapping: 2.223200 ms MarkObjects: 191.118600 ms  DeleteObjects: 5.158900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.582 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 29.08 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.627 seconds
Domain Reload Profiling: 3182ms
	BeginReloadAssembly (405ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (54ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (14ms)
	initialDomainReloadingComplete (62ms)
	LoadAllAssembliesAndSetupDomain (1026ms)
		LoadAssemblies (861ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (346ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (306ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1628ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1225ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (303ms)
			ProcessInitializeOnLoadAttributes (737ms)
			ProcessInitializeOnLoadMethodAttributes (141ms)
			AfterProcessingInitializeOnLoad (36ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (43ms)
Refreshing native plugins compatible for Editor in 243.52 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9081.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 79.042500 ms (FindLiveObjects: 3.911400 ms CreateObjectMapping: 12.726400 ms MarkObjects: 43.634300 ms  DeleteObjects: 18.768500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.601 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 35.24 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.328 seconds
Domain Reload Profiling: 2901ms
	BeginReloadAssembly (386ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (41ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (78ms)
	LoadAllAssembliesAndSetupDomain (1029ms)
		LoadAssemblies (868ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (353ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (313ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1328ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1009ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (306ms)
			ProcessInitializeOnLoadAttributes (527ms)
			ProcessInitializeOnLoadMethodAttributes (141ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 54.23 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (5.0 MB). Loaded Objects now: 9083.
Memory consumption went from 318.1 MB to 313.1 MB.
Total: 17.668600 ms (FindLiveObjects: 1.275500 ms CreateObjectMapping: 1.079600 ms MarkObjects: 11.176800 ms  DeleteObjects: 4.135500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.571 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 48.84 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.489 seconds
Domain Reload Profiling: 4027ms
	BeginReloadAssembly (942ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (180ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (153ms)
	RebuildCommonClasses (102ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (157ms)
	LoadAllAssembliesAndSetupDomain (1311ms)
		LoadAssemblies (1357ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (401ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (327ms)
			ResolveRequiredComponents (47ms)
	FinalizeReload (1490ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1128ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (335ms)
			ProcessInitializeOnLoadAttributes (568ms)
			ProcessInitializeOnLoadMethodAttributes (188ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (51ms)
Refreshing native plugins compatible for Editor in 104.73 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9085.
Memory consumption went from 318.1 MB to 315.2 MB.
Total: 13.671000 ms (FindLiveObjects: 1.447700 ms CreateObjectMapping: 1.079000 ms MarkObjects: 7.900100 ms  DeleteObjects: 3.243000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.637 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 32.85 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.353 seconds
Domain Reload Profiling: 2958ms
	BeginReloadAssembly (395ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (82ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (79ms)
	LoadAllAssembliesAndSetupDomain (1058ms)
		LoadAssemblies (844ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (385ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (339ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1354ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1034ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (272ms)
			ProcessInitializeOnLoadAttributes (569ms)
			ProcessInitializeOnLoadMethodAttributes (162ms)
			AfterProcessingInitializeOnLoad (23ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 140.37 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9087.
Memory consumption went from 318.1 MB to 315.3 MB.
Total: 45.078600 ms (FindLiveObjects: 5.791600 ms CreateObjectMapping: 4.250600 ms MarkObjects: 22.834500 ms  DeleteObjects: 12.199600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.645 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 29.58 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.480 seconds
Domain Reload Profiling: 3100ms
	BeginReloadAssembly (454ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (45ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (63ms)
	RebuildNativeTypeToScriptingClass (28ms)
	initialDomainReloadingComplete (67ms)
	LoadAllAssembliesAndSetupDomain (1007ms)
		LoadAssemblies (899ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (354ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (315ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1481ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1115ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (331ms)
			ProcessInitializeOnLoadAttributes (603ms)
			ProcessInitializeOnLoadMethodAttributes (148ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 958.71 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.1 MB). Loaded Objects now: 9089.
Memory consumption went from 318.2 MB to 315.1 MB.
Total: 30.025200 ms (FindLiveObjects: 2.074300 ms CreateObjectMapping: 1.900900 ms MarkObjects: 18.439400 ms  DeleteObjects: 7.609000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 52.69 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (3.7 MB). Loaded Objects now: 9089.
Memory consumption went from 317.8 MB to 314.2 MB.
Total: 22.637600 ms (FindLiveObjects: 2.176000 ms CreateObjectMapping: 1.948600 ms MarkObjects: 12.904500 ms  DeleteObjects: 5.606800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 52.49 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (6.0 MB). Loaded Objects now: 9089.
Memory consumption went from 317.8 MB to 311.9 MB.
Total: 23.165700 ms (FindLiveObjects: 1.190600 ms CreateObjectMapping: 1.159500 ms MarkObjects: 15.652000 ms  DeleteObjects: 5.162100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.011 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 39.74 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.336 seconds
Domain Reload Profiling: 3308ms
	BeginReloadAssembly (578ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (116ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (74ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (87ms)
	LoadAllAssembliesAndSetupDomain (1221ms)
		LoadAssemblies (1108ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (375ms)
			TypeCache.Refresh (18ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (336ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1336ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (970ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (284ms)
			ProcessInitializeOnLoadAttributes (511ms)
			ProcessInitializeOnLoadMethodAttributes (141ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (40ms)
Refreshing native plugins compatible for Editor in 43.95 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9091.
Memory consumption went from 318.1 MB to 315.3 MB.
Total: 56.825100 ms (FindLiveObjects: 1.400700 ms CreateObjectMapping: 12.672100 ms MarkObjects: 38.416600 ms  DeleteObjects: 4.334100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 298.49 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (2.9 MB). Loaded Objects now: 9091.
Memory consumption went from 317.9 MB to 315.0 MB.
Total: 135.413700 ms (FindLiveObjects: 1.326500 ms CreateObjectMapping: 5.705900 ms MarkObjects: 37.714900 ms  DeleteObjects: 90.664400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.281 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 65.63 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.687 seconds
Domain Reload Profiling: 3926ms
	BeginReloadAssembly (628ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (85ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (100ms)
	RebuildNativeTypeToScriptingClass (45ms)
	initialDomainReloadingComplete (104ms)
	LoadAllAssembliesAndSetupDomain (1362ms)
		LoadAssemblies (1275ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (416ms)
			TypeCache.Refresh (21ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (359ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1688ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1215ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (375ms)
			ProcessInitializeOnLoadAttributes (630ms)
			ProcessInitializeOnLoadMethodAttributes (170ms)
			AfterProcessingInitializeOnLoad (29ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (46ms)
Refreshing native plugins compatible for Editor in 9815.03 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9093.
Memory consumption went from 318.2 MB to 315.3 MB.
Total: 484.353400 ms (FindLiveObjects: 4.875400 ms CreateObjectMapping: 3.386200 ms MarkObjects: 18.732300 ms  DeleteObjects: 457.358000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.748 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 41.27 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.816 seconds
Domain Reload Profiling: 3539ms
	BeginReloadAssembly (480ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (46ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (77ms)
	RebuildCommonClasses (74ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (64ms)
	LoadAllAssembliesAndSetupDomain (1088ms)
		LoadAssemblies (974ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (376ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (332ms)
			ResolveRequiredComponents (21ms)
	FinalizeReload (1816ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1390ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (607ms)
			ProcessInitializeOnLoadAttributes (597ms)
			ProcessInitializeOnLoadMethodAttributes (149ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (41ms)
Refreshing native plugins compatible for Editor in 78.30 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.8 MB). Loaded Objects now: 9095.
Memory consumption went from 318.2 MB to 314.4 MB.
Total: 21.186100 ms (FindLiveObjects: 1.530500 ms CreateObjectMapping: 1.968900 ms MarkObjects: 10.981500 ms  DeleteObjects: 6.703800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 4883.768080 seconds.
  path: Assets/_Game/Resources/Items/ExoSuite.asset
  artifactKey: Guid(9ce720979c2abd144b16fb563894c2aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/_Game/Resources/Items/ExoSuite.asset using Guid(9ce720979c2abd144b16fb563894c2aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2fca4838197f51c40110cf434b155d53') in 0.6455978 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.808 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 44.93 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.478 seconds
Domain Reload Profiling: 3249ms
	BeginReloadAssembly (516ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (73ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (141ms)
	RebuildCommonClasses (72ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (77ms)
	LoadAllAssembliesAndSetupDomain (1087ms)
		LoadAssemblies (911ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (370ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (6ms)
			BuildScriptInfoCaches (330ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1479ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1126ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (314ms)
			ProcessInitializeOnLoadAttributes (627ms)
			ProcessInitializeOnLoadMethodAttributes (151ms)
			AfterProcessingInitializeOnLoad (25ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 277.48 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (3.5 MB). Loaded Objects now: 9097.
Memory consumption went from 318.2 MB to 314.7 MB.
Total: 44.420500 ms (FindLiveObjects: 1.313500 ms CreateObjectMapping: 2.000400 ms MarkObjects: 12.435000 ms  DeleteObjects: 28.669800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.462 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 26.47 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.710 seconds
Domain Reload Profiling: 3146ms
	BeginReloadAssembly (368ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (47ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (937ms)
		LoadAssemblies (805ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (309ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (276ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1710ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1411ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (500ms)
			ProcessInitializeOnLoadAttributes (634ms)
			ProcessInitializeOnLoadMethodAttributes (229ms)
			AfterProcessingInitializeOnLoad (42ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 454.13 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9099.
Memory consumption went from 318.2 MB to 315.3 MB.
Total: 97.238700 ms (FindLiveObjects: 50.800300 ms CreateObjectMapping: 2.527500 ms MarkObjects: 33.098600 ms  DeleteObjects: 10.810200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.644 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 34.81 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.525 seconds
Domain Reload Profiling: 3137ms
	BeginReloadAssembly (417ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (85ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (65ms)
	LoadAllAssembliesAndSetupDomain (1056ms)
		LoadAssemblies (897ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (335ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (297ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1526ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1082ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (24ms)
			BeforeProcessingInitializeOnLoad (289ms)
			ProcessInitializeOnLoadAttributes (576ms)
			ProcessInitializeOnLoadMethodAttributes (165ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (45ms)
Refreshing native plugins compatible for Editor in 118.90 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (4.3 MB). Loaded Objects now: 9101.
Memory consumption went from 318.2 MB to 313.9 MB.
Total: 52.152000 ms (FindLiveObjects: 2.054700 ms CreateObjectMapping: 3.312700 ms MarkObjects: 12.348500 ms  DeleteObjects: 34.434400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.861 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 38.97 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.532 seconds
Domain Reload Profiling: 3366ms
	BeginReloadAssembly (441ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (105ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (1266ms)
		LoadAssemblies (994ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (451ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (408ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1532ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1143ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (367ms)
			ProcessInitializeOnLoadAttributes (587ms)
			ProcessInitializeOnLoadMethodAttributes (154ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (44ms)
Refreshing native plugins compatible for Editor in 52.74 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (4.4 MB). Loaded Objects now: 9103.
Memory consumption went from 318.2 MB to 313.7 MB.
Total: 23.490000 ms (FindLiveObjects: 1.471100 ms CreateObjectMapping: 1.635700 ms MarkObjects: 11.915800 ms  DeleteObjects: 8.465600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.831 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 60.51 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.221 seconds
Domain Reload Profiling: 4022ms
	BeginReloadAssembly (433ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (51ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (88ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (69ms)
	LoadAllAssembliesAndSetupDomain (2224ms)
		LoadAssemblies (2053ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (364ms)
			TypeCache.Refresh (16ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (323ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1221ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (897ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (241ms)
			ProcessInitializeOnLoadAttributes (504ms)
			ProcessInitializeOnLoadMethodAttributes (125ms)
			AfterProcessingInitializeOnLoad (21ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (34ms)
Refreshing native plugins compatible for Editor in 40.63 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (4.0 MB). Loaded Objects now: 9105.
Memory consumption went from 318.2 MB to 314.2 MB.
Total: 15.683100 ms (FindLiveObjects: 1.482800 ms CreateObjectMapping: 1.184000 ms MarkObjects: 9.035900 ms  DeleteObjects: 3.978100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 33.90 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 17 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7858 unused Assets / (5.7 MB). Loaded Objects now: 9105.
Memory consumption went from 317.9 MB to 312.2 MB.
Total: 14.302000 ms (FindLiveObjects: 1.211900 ms CreateObjectMapping: 0.807100 ms MarkObjects: 8.895100 ms  DeleteObjects: 3.386800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.879 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 41.81 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.577 seconds
Domain Reload Profiling: 3416ms
	BeginReloadAssembly (598ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (112ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (111ms)
	RebuildCommonClasses (73ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (79ms)
	LoadAllAssembliesAndSetupDomain (1071ms)
		LoadAssemblies (914ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (351ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (310ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1577ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1229ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (291ms)
			ProcessInitializeOnLoadAttributes (749ms)
			ProcessInitializeOnLoadMethodAttributes (155ms)
			AfterProcessingInitializeOnLoad (26ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (42ms)
Refreshing native plugins compatible for Editor in 35.57 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (7.3 MB). Loaded Objects now: 9107.
Memory consumption went from 318.3 MB to 311.0 MB.
Total: 18.392300 ms (FindLiveObjects: 1.574200 ms CreateObjectMapping: 1.089900 ms MarkObjects: 8.922800 ms  DeleteObjects: 6.803800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.701 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 37.96 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.672 seconds
Domain Reload Profiling: 4352ms
	BeginReloadAssembly (442ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (95ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (91ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (68ms)
	LoadAllAssembliesAndSetupDomain (1102ms)
		LoadAssemblies (918ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (352ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (7ms)
			BuildScriptInfoCaches (312ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (2672ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (2158ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (57ms)
			BeforeProcessingInitializeOnLoad (1206ms)
			ProcessInitializeOnLoadAttributes (690ms)
			ProcessInitializeOnLoadMethodAttributes (169ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (78ms)
Refreshing native plugins compatible for Editor in 35.96 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (5.1 MB). Loaded Objects now: 9109.
Memory consumption went from 318.3 MB to 313.2 MB.
Total: 13.814900 ms (FindLiveObjects: 1.188100 ms CreateObjectMapping: 0.801700 ms MarkObjects: 8.129400 ms  DeleteObjects: 3.694300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.549 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 29.22 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.491 seconds
Domain Reload Profiling: 3017ms
	BeginReloadAssembly (393ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (46ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (64ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (95ms)
	LoadAllAssembliesAndSetupDomain (952ms)
		LoadAssemblies (863ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (286ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (4ms)
			BuildScriptInfoCaches (247ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1494ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1157ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (330ms)
			ProcessInitializeOnLoadAttributes (599ms)
			ProcessInitializeOnLoadMethodAttributes (187ms)
			AfterProcessingInitializeOnLoad (31ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (51ms)
Refreshing native plugins compatible for Editor in 36.85 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (5.2 MB). Loaded Objects now: 9111.
Memory consumption went from 318.3 MB to 313.1 MB.
Total: 15.221400 ms (FindLiveObjects: 1.301700 ms CreateObjectMapping: 1.281800 ms MarkObjects: 8.805200 ms  DeleteObjects: 3.831300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  2.849 seconds
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.UIElement.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.UI.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Configuration.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.Core.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Assets/_Game/Scripts/Inv/InvTooltipSystem.ContextMenu.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.render-pipelines.high-definition/Editor/Wizard/HDWizard.Window.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 40.26 ms, found 34 plugins.
Native extension for WindowsStandalone target not found
[MODES] ModeService[none].Initialize
[MODES] ModeService[none].LoadModes
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.980 seconds
Domain Reload Profiling: 4764ms
	BeginReloadAssembly (794ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (136ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (172ms)
	RebuildCommonClasses (77ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (127ms)
	LoadAllAssembliesAndSetupDomain (1763ms)
		LoadAssemblies (1513ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (565ms)
			TypeCache.Refresh (20ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (510ms)
			ResolveRequiredComponents (23ms)
	FinalizeReload (1980ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1529ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (2ms)
			SetLoadedEditorAssemblies (9ms)
			BeforeProcessingInitializeOnLoad (476ms)
			ProcessInitializeOnLoadAttributes (806ms)
			ProcessInitializeOnLoadMethodAttributes (208ms)
			AfterProcessingInitializeOnLoad (28ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (61ms)
Refreshing native plugins compatible for Editor in 66.32 ms, found 34 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 19 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7908 unused Assets / (2.9 MB). Loaded Objects now: 9113.
Memory consumption went from 318.3 MB to 315.5 MB.
Total: 27.309300 ms (FindLiveObjects: 1.861100 ms CreateObjectMapping: 5.463300 ms MarkObjects: 13.465500 ms  DeleteObjects: 6.517900 ms)

Prepare: number of updated asset objects reloaded= 0
