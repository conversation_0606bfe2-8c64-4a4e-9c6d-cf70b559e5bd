using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System.Collections;

// Add missing reference
using Inventory;

public class ShopSystem : MonoBehaviour
{
    [SerializeField] private EquipmentManager equipmentManager;
    [SerializeField] private ShopUI shopUI;
    [SerializeField] private PlayerStatus playerStatus;
    [SerializeField] private UIPanelManager uiPanelManager;

    private InvItemContainer sellContainer;
    private TraderInventory activeTrader;
    private bool isShopOpen;
    private bool isInSellMode = false;
    private CurrencyManager currencyManager;

    // Common grid dimensions
    private const int SELL_GRID_WIDTH = 6;
    private const int SELL_GRID_HEIGHT = 3;

    public InvItemContainer GetSellContainer()
    {
        if (sellContainer == null)
        {
            sellContainer = new InvItemContainer(SELL_GRID_WIDTH, SELL_GRID_HEIGHT);
        }
        return sellContainer;
    }

    public bool IsShopOpen() => isShopOpen;
    public bool IsInSellMode() => isInSellMode;
    public TraderInventory GetActiveTrader() => activeTrader;

    // Initialize the sell container with custom dimensions
    public void InitializeSellContainer(int width, int height)
    {
        sellContainer = new InvItemContainer(width, height);
    }

    private void Start()
    {
        // Find CurrencyManager first
        currencyManager = CurrencyManager.Instance;

        // Ensure UI Panel Manager is available
        if (uiPanelManager == null)
        {
            uiPanelManager = FindObjectOfType<UIPanelManager>();
        }

        // Initialize sell container
        sellContainer = new InvItemContainer(SELL_GRID_WIDTH, SELL_GRID_HEIGHT);

        // Find player status if not set
        if (playerStatus == null)
        {
            playerStatus = FindObjectOfType<PlayerStatus>();
        }

        // Make sure shop is closed at start
        isShopOpen = false;
        if (uiPanelManager != null)
        {
            uiPanelManager.CloseShop();
        }
    }

    // Set the active trader from the interactable
    public void SetTrader(TraderInventory trader)
    {
        if (trader == null)
        {
            Debug.LogWarning("SetTrader: Trying to set null trader!");
            return;
        }

        activeTrader = trader;

        // Update UI if shop is open
        if (isShopOpen && shopUI != null)
        {
            shopUI.UpdateUI();
        }
    }

    public void ToggleShop()
    {
        Debug.Log("Toggling shop");
        isShopOpen = !isShopOpen;

        // Clear containers
        if (sellContainer != null)
        {
            sellContainer.Clear();
        }

        isInSellMode = false;

        if (isShopOpen)
        {
            if (uiPanelManager != null)
            {
                uiPanelManager.OpenShop();
            }
        }
        else
        {
            if (uiPanelManager != null)
            {
                uiPanelManager.CloseShop();
            }
        }

        if (shopUI != null)
        {
            shopUI.UpdateUI();
        }
    }

    // Get all available items from current trader
    public List<Item> GetAvailableItems()
    {
        if (activeTrader == null)
        {
            return new List<Item>();
        }

        return activeTrader.GetAvailableItems();
    }

    // Get the name of the current trader
    public string GetCurrentTraderName()
    {
        return activeTrader != null ? activeTrader.traderName : "Trader";
    }

    // Get current trader's icon
    public Sprite GetCurrentTraderIcon()
    {
        return activeTrader != null ? activeTrader.traderIcon : null;
    }

    public bool HasEnoughMoney(int amount)
    {
        // Always use CurrencyManager
        if (currencyManager != null)
        {
            return currencyManager.HasSufficientCurrency(amount);
        }

        // Create CurrencyManager if missing
        Debug.LogError("ShopSystem: CurrencyManager not found! Creating one to ensure currency system works.");
        GameObject cmObj = new GameObject("CurrencyManager");
        currencyManager = cmObj.AddComponent<CurrencyManager>();
        return currencyManager.HasSufficientCurrency(amount);
    }

    public void DeductMoney(int amount)
    {
        // Always use CurrencyManager
        if (currencyManager != null)
        {
            currencyManager.TrySpendCurrency(amount);
            return;
        }

        // Create CurrencyManager if missing
        Debug.LogError("ShopSystem: CurrencyManager not found! Creating one to ensure currency system works.");
        GameObject cmObj = new GameObject("CurrencyManager");
        currencyManager = cmObj.AddComponent<CurrencyManager>();
        currencyManager.TrySpendCurrency(amount);
    }

    // Get a reference to the player inventory
    private InvItemContainer GetPlayerInventory()
    {
        // First try to get it from equipped bags
        var storageContainers = GetStorageContainers();
        if (storageContainers.Count > 0 && storageContainers[0].storageContainer != null)
        {
            return storageContainers[0].storageContainer;
        }

        // Cannot use PlayerInvManager directly as the class doesn't exist
        // Instead, try to find a way to get player inventory directly
        var invUI = FindObjectOfType<InvUI>();
        if (invUI != null)
        {
            // Try to get the container through reflection or other means
            // For now, we'll have to rely on the equipment manager's storage containers
            Debug.LogWarning("ShopSystem: Falling back to equipment manager for inventory access");
        }

        return null;
    }

    // Updated implementation of AddItemToPlayerInventory that doesn't duplicate the original method
    public bool AddItemToPlayerInventory(Item item, int quantity)
    {
        if (item == null) return false;

        // Special handling for equipment items (like bags, helmets, and chest armor)
        if (item is EquipmentBase equipment)
        {
            Debug.Log($"Handling equipment purchase: {item.itemName}");

            // For equipment items, we need to use the equipment manager
            if (equipmentManager == null)
            {
                equipmentManager = FindObjectOfType<EquipmentManager>();
                if (equipmentManager == null)
                {
                    Debug.LogError("ShopSystem: EquipmentManager not found!");
                    return false;
                }
            }

            // Check if there's already an item equipped in this slot
            var slot = equipmentManager.GetEquipmentSlot(equipment.Slot);
            if (slot != null && slot.equippedItem != null)
            {
                // Already have something equipped, try to add to inventory instead
                Debug.Log($"Equipment slot {equipment.Slot} already has {slot.equippedItem.itemName} equipped. Adding new {item.itemName} to inventory instead.");

                var playerInventory = GetPlayerInventory();
                if (playerInventory != null)
                {
                    // Special handling for multi-slot equipment items
                    // Try to find space, even if we need to rotate the item
                    Vector2Int? position = playerInventory.FindSpaceForItem(item, true);

                    if (position.HasValue)
                    {
                        Debug.Log($"Found space for {item.itemName} at position {position.Value}, attempting to add");

                        // First try not rotated
                        if (playerInventory.AddItemToSlot(item, quantity, position.Value, false))
                        {
                            Debug.Log($"Successfully added {item.itemName} to inventory at position {position.Value}");
                            SavePlayerData();
                            return true;
                        }
                        // If not successful, try rotated
                        else if (playerInventory.AddItemToSlot(item, quantity, position.Value, true))
                        {
                            Debug.Log($"Successfully added rotated {item.itemName} to inventory at position {position.Value}");
                            SavePlayerData();
                            return true;
                        }
                        else
                        {
                            // This is strange - we found space but couldn't add the item
                            Debug.LogError($"Critical error: Found space for {item.itemName} at {position.Value} but failed to add item!");

                            // Try direct approach with exact grid position
                            int index = ContainerGrid.GridPositionToIndex(position.Value, playerInventory.GridWidth);
                            if (playerInventory.AddItemToSlot(item, quantity, index))
                            {
                                Debug.Log($"Alternative method: Successfully added {item.itemName} to inventory at index {index}");
                                SavePlayerData();
                                return true;
                            }
                        }
                    }
                    else
                    {
                        // For debugging purposes, print the inventory layout
                        Debug.LogWarning($"Could not find space for {item.itemName} in inventory. Grid size: {playerInventory.GridWidth}x{playerInventory.GridHeight}");
                        var items = playerInventory.GetItems();
                        Debug.LogWarning($"Current inventory has {items.Count} items");

                        // Last resort workaround - drop the purchased item in the world
                        Debug.LogWarning($"WORKAROUND: No space for {item.itemName} in inventory, attempting to spawn it in the world");
                        var itemDropping = FindObjectOfType<InvItemDropping>();
                        if (itemDropping != null)
                        {
                            // Get player position to drop near them
                            Vector3 dropPosition = FindObjectOfType<PlayerStatus>()?.transform.position ?? new Vector3(0, 1, 0);
                            dropPosition += Vector3.up * 0.5f; // Slightly above ground

                            // Create the dropped item in the world
                            var droppedItem = itemDropping.DropItem(item, quantity);
                            if (droppedItem != null)
                            {
                                // Position the dropped item at the calculated position
                                droppedItem.transform.position = dropPosition;
                                Debug.Log($"Successfully dropped purchased {item.itemName} in the world. Player can pick it up later.");
                                SavePlayerData();
                                return true; // Consider the purchase successful so it completes
                            }
                        }
                    }

                    return false;
                }
                else
                {
                    Debug.LogWarning($"No inventory found for {item.itemName}");
                    return false;
                }
            }

            // Nothing equipped in this slot, try to equip directly
            bool equipped = equipmentManager.EquipItem(equipment);
            if (equipped)
            {
                Debug.Log($"Successfully equipped {item.itemName}");
                SavePlayerData();

                // Notify the ToolSelectionManager about the new item if it's a tool
                var toolSelectionManager = UnityEngine.Object.FindObjectOfType<ToolSelectionManager>();
                if (toolSelectionManager != null && item is ToolDefinition)
                {
                    // Force a scan for tools since this might be equipped directly
                    toolSelectionManager.ForceToolScan(true);
                }

                return true;
            }
            else
            {
                // Fallback to inventory if equipping fails
                var playerInventory = GetPlayerInventory();
                if (playerInventory != null && playerInventory.FindSpaceForItem(item) != null)
                {
                    bool added = playerInventory.AddItem(item, quantity);
                    if (added)
                    {
                        Debug.Log($"Added equipment {item.itemName} to inventory (not equipped)");
                        SavePlayerData();
                        return true;
                    }
                }
            }

            Debug.LogWarning($"Failed to add equipment {item.itemName} to inventory - equipment requires special handling");
            return false;
        }

        // Normal items go to player inventory
        var container = GetPlayerInventory();
        if (container == null)
        {
            Debug.LogError("ShopSystem: Player inventory container not found!");
            return false;
        }

        // Try to add to player inventory
        bool addedToInventory = container.AddItem(item, quantity);
        if (addedToInventory)
        {
            Debug.Log($"Added {quantity}x {item.itemName} to player inventory");
            SavePlayerData();

            // Notify the ToolSelectionManager about the new item
            var toolSelectionManager = UnityEngine.Object.FindObjectOfType<ToolSelectionManager>();
            if (toolSelectionManager != null)
            {
                // Check if the item is a tool and update the tool selection
                toolSelectionManager.CheckNewItem(item, true);
            }

            return true;
        }

        Debug.LogError($"ShopSystem: Failed to add {item.itemName} to player inventory");
        return false;
    }

    // Helper method to save player data
    private void SavePlayerData()
    {
        // Save inventory change - use PlayerProgressionManager
        var progressionManager = PersistenceManager.Instance ?? FindObjectOfType<PlayerProgressionManager>();
        if (progressionManager != null)
        {
            progressionManager.SaveInventoryAndEquipmentNow();
            Debug.Log("Saved player inventory data after adding item");
        }
        else
        {
            Debug.LogWarning("PlayerProgressionManager not found - changes might not be saved");
        }
    }

    // Get storage containers from equipment manager
    private List<EquipmentManager.EquipmentSlot> GetStorageContainers()
    {
        var slots = new List<EquipmentManager.EquipmentSlot>();
        foreach (var slotType in System.Enum.GetValues(typeof(EquipmentSlotType)).Cast<EquipmentSlotType>())
        {
            var slot = equipmentManager.GetEquipmentSlot(slotType);
            if (slot != null && slot.equippedItem != null &&
                slot.equippedItem is Bag &&
                slot.storageContainer != null)
            {
                slots.Add(slot);
            }
        }
        return slots;
    }

    // Get the sale price for an item
    public int GetSellValueForItem(Item item, int quantity = 1)
    {
        if (item == null)
        {
            Debug.LogError("Attempted to get sell value for null item");
            return 0;
        }

        // Get the base price from active trader if available
        int itemBasePrice;

        if (activeTrader != null)
        {
            // Use trader's pricing
            itemBasePrice = activeTrader.GetSellingPriceForItem(item);
        }
        else
        {
            // Fall back to Item.Price
            itemBasePrice = item.Price;
        }

        // Calculate total value
        int totalValue = itemBasePrice * quantity;

        Debug.Log($"Calculated sell value for {quantity}x {item.itemName}: {itemBasePrice}/item, total {totalValue}");

        return totalValue;
    }

    public void ConfirmSale()
    {
        var sellContainer = GetSellContainer();
        if (sellContainer == null) return;

        // Calculate total value
        int totalValue = 0;
        int itemCount = 0;

        foreach (var stack in sellContainer.GetItems())
        {
            if (stack?.Item != null)
            {
                itemCount += stack.Quantity;

                // Use the correct method to calculate the sell value
                int itemValue = GetSellValueForItem(stack.Item, stack.Quantity);
                totalValue += itemValue;
            }
        }

        // Add money to player
        if (totalValue > 0)
        {
            // Always use CurrencyManager
            if (currencyManager != null)
            {
                currencyManager.AddCurrency(totalValue);
            }
            else
            {
                // Create CurrencyManager if missing
                Debug.LogError("ShopSystem: CurrencyManager not found in ConfirmSale! Creating one to ensure currency system works.");
                GameObject cmObj = new GameObject("CurrencyManager");
                currencyManager = cmObj.AddComponent<CurrencyManager>();
                currencyManager.AddCurrency(totalValue);
            }

            if (shopUI != null)
            {
                shopUI.ShowSoldMessage(totalValue);
            }
        }

        // Clear sell container
        sellContainer.Clear();

        // Update UI
        if (shopUI != null)
        {
            shopUI.UpdateUI();
        }

        // Play a coin sound
        var audioSource = FindObjectOfType<MoneyCounterAnimation>()?.GetComponent<AudioSource>();
        if (audioSource != null && audioSource.clip != null)
        {
            // Play the sound at a slightly louder volume for the sale completion
            float originalVolume = audioSource.volume;
            audioSource.volume = originalVolume * 1.5f;
            audioSource.Play();

            // Reset the volume after playing
            StartCoroutine(ResetVolumeAfterDelay(audioSource, originalVolume, 0.1f));
        }

        Debug.Log($"ConfirmSale: Successfully sold {itemCount} items for {totalValue} €");
    }

    private System.Collections.IEnumerator ResetVolumeAfterDelay(AudioSource source, float originalVolume, float delay)
    {
        yield return new WaitForSeconds(delay);
        if (source != null)
        {
            source.volume = originalVolume;
        }
    }

    public void SetSellMode(bool sellMode)
    {
        // Update sell mode state
        isInSellMode = sellMode;

        // If entering sell mode, ensure sell container is initialized
        if (sellMode)
        {
            if (sellContainer == null)
            {
                sellContainer = new InvItemContainer(SELL_GRID_WIDTH, SELL_GRID_HEIGHT);
            }
            else
            {
                // Clear the sell container when activating sell mode
                sellContainer.Clear();
            }
        }

        // Notify the shop UI
        if (shopUI != null)
        {
            shopUI.SetShopMode(sellMode);
        }

        // Ensure the InvShiftClickHandler is aware of the change
        // by requesting a UI refresh, which will make it check the current state
        // InvShiftClickHandler is not a MonoBehaviour, can't use FindObjectOfType
        var invUI = FindObjectOfType<InvUI>();
        if (invUI != null)
        {
            invUI.UpdateUI();
        }

        Debug.Log($"ShopSystem: Sell mode set to {sellMode}");
    }

    // Get the purchase price for an item
    public int GetItemPrice(Item item)
    {
        if (item == null) return 0;

        // Check if this item has a custom price in the active trader
        if (activeTrader != null)
        {
            var stockItems = activeTrader.GetStockItems();
            foreach (var stockItem in stockItems)
            {
                if (stockItem.item == item && stockItem.customPrice > 0)
                {
                    return stockItem.customPrice;
                }
            }
        }

        // Otherwise use the default item price
        return item.Price;
    }

    // Modified version to handle individual instances being purchased
    public bool PurchaseItem(Item item, int quantity = 1, bool reduceStock = true)
    {
        if (item == null) return false;

        // Calculate cost
        int itemPrice = GetItemPrice(item);
        int totalCost = itemPrice * quantity;

        // Check if player has enough money
        if (!HasEnoughMoney(totalCost))
        {
            Debug.LogWarning($"Not enough money to purchase {quantity}x {item.itemName}. Requires {totalCost}");
            return false;
        }

        // Check if the trader has enough of this item (for non-unlimited items)
        if (activeTrader != null && reduceStock)
        {
            var stockItems = activeTrader.GetStockItems();
            bool itemFound = false;

            foreach (var stockItem in stockItems)
            {
                if (stockItem.item == item)
                {
                    itemFound = true;

                    // Check quantity if not unlimited
                    if (!stockItem.unlimited && stockItem.quantity < quantity)
                    {
                        Debug.LogWarning($"Trader doesn't have enough {item.itemName}. Available: {stockItem.quantity}, Requested: {quantity}");
                        return false;
                    }
                    break;
                }
            }

            if (!itemFound)
            {
                Debug.LogWarning($"Item {item.itemName} not found in trader's inventory.");
                return false;
            }
        }

        // Try to add to inventory
        bool added = AddItemToPlayerInventory(item, quantity);
        if (!added)
        {
            Debug.LogWarning($"Could not add {item.itemName} to inventory - not enough space");
            return false;
        }

        // Deduct money - use CurrencyManager if available
        DeductMoney(totalCost);

        // Currency is saved automatically by CurrencyManager, no need to call SavePlayerCurrency

        // Reduce trader's stock quantity if needed
        if (activeTrader != null && reduceStock)
        {
            activeTrader.ReduceItemQuantity(item, quantity);
        }

        // Log success
        Debug.Log($"Purchase successful: {quantity}x {item.itemName} for {totalCost}");

        // Update UI
        if (shopUI != null)
        {
            shopUI.UpdateUI();
        }

        return true;
    }

    // Add a debug method to help diagnose inventory space issues
    private void DebugInventorySpace(InvItemContainer inventory, Item item)
    {
        if (inventory == null || item == null) return;

        Debug.Log($"===== INVENTORY SPACE DEBUG START =====");
        Debug.Log($"Checking space for item: {item.itemName}");
        Debug.Log($"Item dimensions: {item.SlotDimension.Width}x{item.SlotDimension.Height}");
        Debug.Log($"Inventory grid size: {inventory.GridWidth}x{inventory.GridHeight}");

        // List all items in inventory
        var items = inventory.GetItems();
        Debug.Log($"Current inventory has {items.Count} items");

        foreach (var stack in items)
        {
            if (inventory.TryGetItemPosition(stack, out Vector2Int pos, out bool isRotated))
            {
                int itemWidth = isRotated ? stack.Item.SlotDimension.Height : stack.Item.SlotDimension.Width;
                int itemHeight = isRotated ? stack.Item.SlotDimension.Width : stack.Item.SlotDimension.Height;

                Debug.Log($"  - Item: {stack.Item.itemName}, Pos: {pos}, " +
                          $"Size: {itemWidth}x{itemHeight}, Rotated: {isRotated}");
            }
        }

        // Try to find space manually and report results
        Vector2Int? foundPos = inventory.FindSpaceForItem(item, true);
        if (foundPos.HasValue)
        {
            Debug.Log($"Found space at position: {foundPos.Value}");
        }
        else
        {
            Debug.Log($"No space found for item {item.itemName}");
        }

        Debug.Log($"===== INVENTORY SPACE DEBUG END =====");
    }

    // Check if item can be added to inventory without actually adding it
    public bool CanAddItemToInventory(Item item, int quantity)
    {
        if (item == null) return false;

        // Special handling for equipment items (like bags)
        if (item is EquipmentBase equipment)
        {
            Debug.Log($"Checking space for equipment: {item.itemName}");

            // Make sure equipment manager is available
            if (equipmentManager == null)
            {
                equipmentManager = FindObjectOfType<EquipmentManager>();
                if (equipmentManager == null)
                {
                    Debug.LogError("ShopSystem: EquipmentManager not found!");
                    return false;
                }
            }

            // Check if the corresponding equipment slot is empty
            var slot = equipmentManager.GetEquipmentSlot(equipment.Slot);
            if (slot != null)
            {
                if (slot.equippedItem == null)
                {
                    // Slot is empty, can equip directly
                    return true;
                }
                else
                {
                    // Slot is already occupied, need to check inventory space
                    Debug.Log($"Equipment slot {equipment.Slot} already occupied by {slot.equippedItem.itemName}, checking inventory space for {item.itemName}");
                }
            }

            // Check if there's inventory space (either because slot is occupied or other issues)
            var playerInventory = GetPlayerInventory();
            if (playerInventory == null)
            {
                Debug.LogError("ShopSystem: Player inventory container not found!");
                return false;
            }

            // Special handling for all equipment items which might be multi-slot
            Debug.Log($"Special equipment item check - using special space finding logic");

            // For debugging help
            DebugInventorySpace(playerInventory, item);

            // For equipment, we need to be extra careful about finding space
            Vector2Int? position = playerInventory.FindSpaceForItem(item, true);
            bool foundSpace = position.HasValue;
            Debug.Log($"Space in inventory for {item.itemName}: {foundSpace}, position: {(foundSpace ? position.ToString() : "none")}");
            return foundSpace;
        }

        // For normal items
        var container = GetPlayerInventory();
        if (container == null)
        {
            Debug.LogError("ShopSystem: Player inventory container not found!");
            return false;
        }

        // Check if there's space for this item
        return container.FindSpaceForItem(item) != null;
    }
}