using UnityEngine;
using UnityEngine.UIElements;
using UnityEngine.SceneManagement;
using KinematicCharacterController.FPS;
using System.Collections;

public class PauseMenuManager : BaseMenuManager
{
    [Header("Pause Settings")]
    [SerializeField] private bool pauseGameOnMenu = true;
    [SerializeField] private FPSCharacterController playerController;
    private bool isPaused = false;
    private bool escKeyEnabled = true;

    [Header("Menu References")]
    private VisualElement rootContainer;
    private Button loseButton;
    private Button returnToTitleButton;
    private Button quitGameButton;
    private Button inviteFriendsButton;
    private Button resumeButton;
    private AudioLowPassFilter lowPassFilter;

    #region Unity Lifecycle

    protected override void Awake()
    {
        base.Awake();
        SceneManager.sceneLoaded += OnSceneLoaded;
    }

    protected override void Start()
    {
        base.Start();
        InitializePauseMenu();
        HideMenu();
        lowPassFilter = FindObjectOfType<AudioLowPassFilter>();
        if (lowPassFilter != null)
        {
            lowPassFilter.enabled = false;
        }

        // Auto-find player controller if not set
        if (playerController == null)
        {
            playerController = FindObjectOfType<FPSCharacterController>();
        }
    }

    protected override void Update()
    {
        base.Update();
        HandleEscapeKey();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        SceneManager.sceneLoaded -= OnSceneLoaded;
    }

    #endregion

    #region Initialization

    protected override void InitializeUIElements()
    {
        base.InitializeUIElements();

        rootContainer = root.Q<VisualElement>("root-container");
        InitializeButtonReferences();
    }

    private void InitializePauseMenu()
    {
        SetupButton("resume-button", ResumeGame);
        SetupButton("return-to-title-button", ReturnToTitle);
        SetupButton("quit-game-button", QuitGame);
    }

    private void InitializeButtonReferences()
    {
        loseButton = root.Q<Button>("lose-button");
        returnToTitleButton = root.Q<Button>("return-to-title-button");
        quitGameButton = root.Q<Button>("quit-game-button");
        resumeButton = root.Q<Button>("resume-button");
    }

    #endregion

    #region Menu State Management

    private void HandleEscapeKey()
    {
        if (!escKeyEnabled) return;

        if (Input.GetKeyDown(KeyCode.Escape))
        {
            // Check for any open UI first
            InvUI invUI = FindObjectOfType<InvUI>();
            UIPanelManager uiPanelManager = FindObjectOfType<UIPanelManager>();
            
            bool isAnyUIOpen = false;
            
            // Check if inventory is open
            if (invUI != null && invUI.IsInventoryVisible())
            {
                invUI.CloseInventory();
                isAnyUIOpen = true;
                
                // Find and reset interactable states
                ResetAllInteractables();
            }
            // Check if shop or stash is open via UI panel manager
            else if (uiPanelManager != null && (uiPanelManager.IsStashOpen || uiPanelManager.IsShopOpen))
            {
                // Close both shop and stash
                uiPanelManager.CloseStash();
                uiPanelManager.CloseShop();
                isAnyUIOpen = true;
                
                // Find and reset interactable states
                ResetAllInteractables();
            }
            
            // Only toggle pause menu if no UI was open
            if (!isAnyUIOpen)
            {
                ToggleMenu();
            }
        }
    }

    private void ToggleMenu()
    {
        if (!isPaused)
            ShowMenu();
        else if (isInSubmenu)
            ShowStartMenu();
        else
            HideMenu();
    }

    private void ShowMenu()
    {
        isPaused = true;
        rootContainer.style.display = DisplayStyle.Flex;
        ShowStartMenu();

        // Only pause time if the setting is enabled
        if (pauseGameOnMenu)
        {
            Time.timeScale = 0;
        }

        // Notify FPSCharacterCamera
        var fpsCamera = FindObjectOfType<FPSCharacterCamera>();
        if (fpsCamera != null)
        {
            fpsCamera.SetPauseState(true);
        }

        // Disable player controller input but keep it enabled for physics
        if (playerController != null)
        {
            playerController.enabled = false;
        }

        // Enable the low-pass filter to muffle the audio
        if (lowPassFilter != null)
        {
            lowPassFilter.enabled = true;
        }
    }

    private void HideMenu()
    {
        isPaused = false;
        rootContainer.style.display = DisplayStyle.None;

        // Restore time scale if it was paused
        if (pauseGameOnMenu)
        {
            Time.timeScale = 1;
        }

        // Notify FPSCharacterCamera
        var fpsCamera = FindObjectOfType<FPSCharacterCamera>();
        if (fpsCamera != null)
        {
            fpsCamera.SetPauseState(false);
        }

        // Re-enable player controller
        if (playerController != null)
        {
            playerController.enabled = true;
        }

        // Disable the low-pass filter to restore normal audio
        if (lowPassFilter != null)
        {
            lowPassFilter.enabled = false;
        }
    }

    public void EnableEscKey()
    {
        escKeyEnabled = true;
    }

    public void DisableEscKey()
    {
        escKeyEnabled = false;
        if (rootContainer != null)
        {
            HideMenu();
        }
    }

    // Reset the state of all interactables in the scene
    private void ResetAllInteractables()
    {
        BaseInteractable[] interactables = FindObjectsOfType<BaseInteractable>();
        foreach (var interactable in interactables)
        {
            if (interactable.IsUIOpen())
            {
                interactable.ForceCloseUI();
                Debug.Log($"Reset UI state for {interactable.gameObject.name}");
            }
        }
    }

    #endregion

    #region Button Actions

    private void ResumeGame()
    {
        HideMenu();
    }

    private void ReturnToTitle()
    {
        SaveCurrentPlayerState();

        // Show and unlock cursor
        if (customCursor != null)
        {
            customCursor.SetInteractiveCursor();
        }
        UnityEngine.Cursor.visible = true;
        UnityEngine.Cursor.lockState = CursorLockMode.None;

        // Restore time scale if it was paused
        if (pauseGameOnMenu)
        {
            Time.timeScale = 1;
        }

        SceneManager.LoadScene("StartMenu");
    }

    private void QuitGame()
    {
        SaveCurrentPlayerState();
        if (GameFlowManager.Instance != null)
        {
            GameFlowManager.Instance.QuitGame();
        }
        else
        {
#if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
#else
        Application.Quit();
#endif
        }
    }

    private void SaveCurrentPlayerState()
    {
        // PlayerProgressionManager handles all saves automatically - no manual save needed
        // It saves position every 0.25 seconds and handles force quits properly
        if (PersistentObject.Instance != null)
        {
            // Save camera rotation before quitting/changing scenes
            SaveCurrentCameraRotation();
            
            // Force an immediate save to ensure latest state is captured
            PersistentObject.Instance.SavePlayerCurrency();
            Debug.Log("[PauseMenu] Triggered immediate save via PlayerProgressionManager");
        }
        else
        {
            Debug.LogWarning("[PauseMenu] PlayerProgressionManager not found - save skipped");
        }
    }
    
    private void SaveCurrentCameraRotation()
    {
        var fpsCamera = FindObjectOfType<FPSCharacterCamera>();
        if (fpsCamera != null && PersistentObject.Instance != null)
        {
            Transform camTransform = fpsCamera.transform;
            Vector3 angles = camTransform.eulerAngles;
            float xRotation = angles.x;
            if (xRotation > 180f)
                xRotation -= 360f;
            
            // Save the camera rotation directly to PlayerProgressionManager
            PersistentObject.Instance.SaveCameraRotation(xRotation, angles.y);
            
            Debug.Log($"[PauseMenu] Saved camera rotation: X={xRotation}, Y={angles.y}");
        }
    }

    #endregion

    #region Scene Management

    private void OnSceneLoaded(Scene scene, LoadSceneMode mode)
    {
        if (scene.name == "Main")
        {
            InitializeUIElements();
            EnableEscKey();
        }
        else if (scene.name == "StartMenu")
        {
            DisableEscKey();
        }
    }

    #endregion

    #region Settings Management

    protected override void ShowSettingsMenu()
    {
        base.ShowSettingsMenu();
        if (titleLabel != null)
        {
            titleLabel.text = "Settings";
        }
    }

    protected override void ShowStartMenu()
    {
        base.ShowStartMenu();
        if (titleLabel != null)
        {
            titleLabel.text = "Paused";
        }
    }

    #endregion

    // Method to toggle pause behavior at runtime if needed
    public void SetPauseGameOnMenu(bool shouldPause)
    {
        pauseGameOnMenu = shouldPause;

        // If we're currently paused, update the time scale
        if (isPaused)
        {
            Time.timeScale = shouldPause ? 0 : 1;
        }
    }
}