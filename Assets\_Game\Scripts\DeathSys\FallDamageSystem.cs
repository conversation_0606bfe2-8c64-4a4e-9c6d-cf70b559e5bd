using UnityEngine;
using System;
using System.Collections;
using KinematicCharacterController;
using KinematicCharacterController.FPS;

public class FallDamageSystem : MonoBehaviour
{
    #region Events
    // Events for other systems to subscribe to
    public event Action OnFallDamageApplied;
    public event Action OnFatalFall;
    public event Action OnDeadlyFallDetected;
    public event Action OnPlayerLanded;
    public event Action<float, Vector3> OnTumbleRequired;
    #endregion

    #region References
    [Header("References")]
    [Tooltip("Reference to the player's character controller")]
    [SerializeField] private FPSCharacterController characterController;
    [SerializeField] private KinematicCharacterMotor characterMotor;
    [SerializeField] private PlayerStatus playerStatus;
    #endregion

    #region Configuration
    [Header("Fall Detection")]
    [Tooltip("Enable/disable fall damage")]
    public bool fallDamageEnabled = true;

    [Tooltip("Minimum fall height in meters before applying damage")]
    public float minFallDamageHeight = 4f;

    [Tooltip("Height in meters that triggers tumbling")]
    public float tumbleFallHeight = 8f;

    [Tooltip("Height in meters that causes instant death")]
    public float lethalFallHeight = 15f;

    [Tooltip("Minimum vertical velocity to apply damage")]
    public float minFallDamageVelocity = 8f;

    [Tooltip("Vertical velocity that triggers tumbling")]
    public float tumbleFallVelocity = 12f;

    [Tooltip("Vertical velocity that causes instant death")]
    public float lethalFallVelocity = 18f;

    [Tooltip("How much height factors into damage calculation (0-1)")]
    [Range(0f, 1f)]
    public float fallHeightWeight = 0.2f;

    [Tooltip("How much vertical velocity factors into damage calculation (0-1)")]
    [Range(0f, 1f)]
    public float fallVelocityWeight = 0.8f;

    [Tooltip("Horizontal speed contribution factor (0-1)")]
    [Range(0f, 1f)]
    public float horizontalSpeedFactor = 0.2f;

    [Header("Energy Protection System")]
    [Tooltip("Reference to BatteryController for accurate segment calculations (auto-found if not assigned)")]
    [SerializeField] private BatteryController batteryController;

    [Tooltip("Energy cost per unit of fall damage (scales with fall severity)")]
    [Range(10f, 50f)]
    public float energyCostPerDamageUnit = 20f;

    [Tooltip("Minimum segments lost before tumbling is triggered (even if energy absorbed damage)")]
    [Range(2, 5)]
    public int tumbleThresholdSegments = 3;

    [Tooltip("Minimum segments required for instant death (when energy buffer insufficient). Energy always protects first.")]
    [Range(5f, 15f)]
    public float deathThresholdSegments = 8f;

    [Header("Debug")]
    [Tooltip("Enable detailed logging for fall damage calculations")]
    public bool debugFallDamage = true;
    #endregion

    #region Energy Calculation Helpers
    /// <summary>
    /// Gets the actual energy per segment by calling BatteryController directly
    /// </summary>
    private float GetActualEnergyPerSegment()
    {
        if (batteryController != null)
        {
            return batteryController.GetActualEnergyPerSegment();
        }

        // Fallback calculation if no battery controller available
        if (playerStatus != null)
        {
            return playerStatus.maxEnergy / 10f; // Assume 10 segments as fallback
        }

        return 150f; // Ultimate fallback
    }
    #endregion

    #region State Tracking
    // Internal state tracking
    private float fallStartY;
    private bool isFalling;
    private float maxFallVelocity;
    private float lastFallDistance;
    private float lastImpactSpeed;
    private bool deadlyFallWarningFired;
    private float minFallTimeToEnableDamage = 0.2f;
    private float fallStartTime;
    private float previousFrameYPosition; // Added to track Y position of the previous frame while falling
    #endregion

    #region Initialization & Lifecycle
    private void Awake()
    {
        // Find required components if not set
        if (characterController == null)
            characterController = GetComponent<FPSCharacterController>();

        if (characterMotor == null)
            characterMotor = GetComponent<KinematicCharacterMotor>();

        if (playerStatus == null)
            playerStatus = GetComponent<PlayerStatus>();

        if (batteryController == null)
            batteryController = BatteryController.EnsureBatteryController();

        // Validate critical components
        if (characterController == null)
            Debug.LogError("FallDamageSystem: No FPSCharacterController component found!");

        if (characterMotor == null)
            Debug.LogError("FallDamageSystem: No KinematicCharacterMotor component found!");
    }

    private void OnEnable()
    {
        // Subscribe to character controller events
        if (characterController != null)
        {
            characterController.OnLandedEvent += OnCharacterLanded;
            characterController.OnLeaveGroundEvent += OnCharacterLeftGround;
        }
    }

    private void OnDisable()
    {
        // Unsubscribe from character controller events
        if (characterController != null)
        {
            characterController.OnLandedEvent -= OnCharacterLanded;
            characterController.OnLeaveGroundEvent -= OnCharacterLeftGround;
        }
    }

    private void Update()
    {
        // Only track falling if we're in the falling state
        if (isFalling)
        {
            float currentYPosition = transform.position.y;
            float fallDuration = Time.time - fallStartTime;

            // Threshold for upward movement to trigger a reset (e.g., 1cm)
            float upwardMovementThreshold = 0.01f;
            // Minimum fall duration before upward movement can trigger a reset (e.g., 0.1 seconds)
            float minFallDurationForResetCheck = 0.1f;

            // Check if player moved upwards significantly after an initial fall period
            if (currentYPosition > previousFrameYPosition + upwardMovementThreshold && fallDuration > minFallDurationForResetCheck)
            {
                if (debugFallDamage)
                {
                    Debug.Log($"[FallDamageSystem] Player moved up from {previousFrameYPosition}m to {currentYPosition}m. Resetting fall parameters.");
                }
                // Reset fall parameters as if starting a new fall from the current position
                fallStartY = currentYPosition;
                fallStartTime = Time.time;
                maxFallVelocity = characterMotor != null ? Mathf.Abs(characterMotor.BaseVelocity.y) : 0f;
                deadlyFallWarningFired = false;
                // previousFrameYPosition will be updated to currentYPosition at the end of this block
            }

            TrackFallingVelocity();

            // Check if fall has become deadly and fire event if needed
            if (!deadlyFallWarningFired && IsCurrentFallDeadly())
            {
                deadlyFallWarningFired = true;
                OnDeadlyFallDetected?.Invoke();
                if (debugFallDamage)
                {
                    Debug.Log("[FallDamageSystem] Deadly fall detected - warning systems");
                }
            }
            
            // Update previousFrameYPosition for the next frame's check
            previousFrameYPosition = currentYPosition;
        }
    }
    #endregion

    #region Fall Tracking
    private void OnCharacterLeftGround()
    {
        // Only start tracking a fall if we didn't jump
        if (!characterController.IsJumpedThisFrame)
        {
            StartFallTracking();
        }
    }

    private void OnCharacterLanded()
    {
        bool shouldPreventTumble = false;

        // Process fall damage if we were tracking a fall
        if (fallDamageEnabled && isFalling && (Time.time - fallStartTime) > minFallTimeToEnableDamage)
        {
            shouldPreventTumble = ProcessFallDamage();

            // Check for tumble unless specifically prevented
            if (!shouldPreventTumble)
            {
                CheckForTumbleRequired(false); // Not forced by energy system
            }
            else if (debugFallDamage)
            {
                Debug.Log("[FallDamageSystem] Tumble prevented - energy absorbed fall damage without triggering tumble threshold");
            }
        }

        // Reset falling state
        ResetFallTracking();

        // Invoke the player landed event for other systems
        OnPlayerLanded?.Invoke();
    }

    private void CheckForTumbleRequired(bool forcedByEnergySystem)
    {
        float fallDistance = lastFallDistance;
        float fallVelocity = maxFallVelocity;
        Vector3 horizontalVelocity = Vector3.zero;

        if (characterMotor != null)
        {
            Vector3 baseVelocity = characterMotor.BaseVelocity;
            horizontalVelocity = new Vector3(baseVelocity.x, 0, baseVelocity.z);
        }

        // Check if player is currently sliding - if so, skip tumble (unless forced by energy system)
        bool isSliding = characterController != null && characterController.CurrentCharacterState == CharacterState.Sliding;
        
        if (isSliding && !forcedByEnergySystem)
        {
            if (debugFallDamage)
            {
                Debug.Log($"[FallDamageSystem] Player is sliding - tumble skipped despite fall conditions (Distance: {fallDistance}m, Velocity: {fallVelocity}m/s)");
            }
            return; // No tumble while sliding, just keep sliding
        }

        // Check if tumbling should happen
        bool shouldTumble = false;
        
        if (forcedByEnergySystem)
        {
            // Energy system determined tumbling should happen - honor that decision
            shouldTumble = true;
            if (debugFallDamage)
            {
                Debug.Log($"[FallDamageSystem] Tumble forced by energy system - Distance: {fallDistance}m, Velocity: {fallVelocity}m/s");
            }
        }
        else
        {
            // Use traditional velocity/height thresholds, but exclude lethal falls
            shouldTumble = (fallDistance >= tumbleFallHeight || fallVelocity >= tumbleFallVelocity) && 
                          fallVelocity < lethalFallVelocity && fallDistance < lethalFallHeight;
            if (shouldTumble && debugFallDamage)
            {
                Debug.Log($"[FallDamageSystem] Tumble triggered by traditional thresholds - Distance: {fallDistance}m, Velocity: {fallVelocity}m/s");
            }
        }

        if (shouldTumble)
        {
            // Trigger tumble event
            OnTumbleRequired?.Invoke(Mathf.Max(fallVelocity, 5f), horizontalVelocity);
        }
    }

    public void StartFallTracking()
    {
        isFalling = true;
        fallStartY = transform.position.y;
        fallStartTime = Time.time;
        maxFallVelocity = characterMotor != null ? Mathf.Abs(characterMotor.BaseVelocity.y) : 0f;
        deadlyFallWarningFired = false;
        previousFrameYPosition = fallStartY; // Initialize with current Y when fall starts or resets

        if (debugFallDamage)
        {
            Debug.Log($"[FallDamageSystem] Fall started or reset at height: {fallStartY}m, initial Y: {previousFrameYPosition}m, velocity: {maxFallVelocity}m/s");
        }
    }

    public void ResetFallTracking()
    {
        isFalling = false;
        maxFallVelocity = 0f;
        deadlyFallWarningFired = false;
    }

    private void TrackFallingVelocity()
    {
        if (characterMotor == null) return;

        // Track maximum velocity during fall
        float currentFallVelocity = Mathf.Abs(characterMotor.BaseVelocity.y);
        if (currentFallVelocity > maxFallVelocity)
        {
            maxFallVelocity = currentFallVelocity;
        }
    }
    #endregion

    #region Damage Processing
    private bool ProcessFallDamage()
    {
        float fallDistance = fallStartY - transform.position.y;

        // Ignore very small falls
        if (fallDistance < 0.2f)
        {
            if (debugFallDamage)
            {
                Debug.Log($"[FallDamageSystem] Ignoring tiny fall: {fallDistance}m");
            }
            return false;
        }

        // Store fall metrics for potential use by other systems
        lastFallDistance = fallDistance;

        // Debug velocity at impact
        if (characterMotor != null)
        {
            Vector3 rawVelocity = characterMotor.BaseVelocity;
            float velocityY = Mathf.Abs(rawVelocity.y);
            float horizontalSpeed = new Vector3(rawVelocity.x, 0, rawVelocity.z).magnitude;
            lastImpactSpeed = horizontalSpeed;

            if (debugFallDamage)
            {
                Debug.Log($"[FallDamageSystem] Fall impact - Height: {fallDistance}m, Raw velocity: {rawVelocity}, " +
                        $"Y velocity: {velocityY}m/s, Horizontal speed: {horizontalSpeed}m/s, " +
                        $"Max fall velocity: {maxFallVelocity}m/s");
            }

            // Apply fall damage and return whether energy absorbed it
            return ApplyFallDamage(fallDistance, maxFallVelocity, horizontalSpeed);
        }

        return false;
    }

    private bool ApplyFallDamage(float fallHeight, float fallVelocity, float horizontalSpeed)
    {
        // If fall distance is below minimum or we don't have a player status component, exit early
        if (fallHeight <= minFallDamageHeight || playerStatus == null)
            return false; // No energy absorbed

        if (debugFallDamage)
        {
            Debug.Log($"[FallDamageSystem] Fall detected - Height: {fallHeight}m, Fall Velocity: {fallVelocity}m/s, Horizontal: {horizontalSpeed}m/s");
        }

        // Velocity-first check: If velocity is extremely low, it's not a dangerous fall
        if (fallVelocity < minFallDamageVelocity * 0.5f)
        {
            if (debugFallDamage)
            {
                Debug.Log($"[FallDamageSystem] Fall ignored - Velocity too low ({fallVelocity}m/s < {minFallDamageVelocity * 0.5f}m/s)");
            }
            return false; // No energy absorbed
        }

        // Check if player is sliding
        bool isSliding = characterController != null && characterController.CurrentCharacterState == CharacterState.Sliding;

        // Note: Removed hard velocity check here - let energy system handle all falls first

        // Calculate danger scores
        float heightDanger = Mathf.InverseLerp(minFallDamageHeight, lethalFallHeight, fallHeight);
        // Allow velocity danger to exceed 1.0 for extreme velocities (uncapped scaling)
        float velocityDanger = Mathf.Max(0f, (fallVelocity - minFallDamageVelocity) / (lethalFallVelocity - minFallDamageVelocity));

        // Calculate combined danger with horizontal speed as a small contributor
        float horizontalDanger = Mathf.InverseLerp(5f, 15f, horizontalSpeed) * horizontalSpeedFactor;
        float combinedDanger = (heightDanger * fallHeightWeight) + (velocityDanger * fallVelocityWeight) + horizontalDanger;

        // Remove the old fatal fall threshold - let the energy system handle everything

        // Sliding protection: treat tumble-level falls as harmless when sliding
        if (isSliding && (fallHeight >= tumbleFallHeight || fallVelocity >= tumbleFallVelocity))
        {
            if (debugFallDamage)
            {
                Debug.Log($"[FallDamageSystem] Player is sliding - treating tumble-level fall as harmless " +
                          $"(Height: {fallHeight}m, Velocity: {fallVelocity}m/s, Danger: {combinedDanger})");
            }
            return false; // No damage while sliding for tumble-level falls
        }

        // Calculate damage units based on combined danger with exponential scaling for high danger
        // This makes big falls much more expensive than small falls
        float damageUnits;
        if (combinedDanger <= 0.5f)
        {
            damageUnits = Mathf.Lerp(0f, 2f, combinedDanger * 2f);  // 0-0.5 danger = 0-2 units (linear)
        }
        else if (combinedDanger <= 1.0f)
        {
            damageUnits = Mathf.Lerp(2f, 15f, Mathf.Pow((combinedDanger - 0.5f) * 2f, 2f)); // 0.5-1.0 danger = 2-15 units (exponential)
        }
        else
        {
            // For extreme dangers > 1.0, continue exponential scaling
            damageUnits = 15f + (combinedDanger - 1.0f) * 25f; // Extreme falls cost 25 units per additional danger point
        }

        // Calculate energy cost to absorb this damage
        float energyCostToAbsorb = damageUnits * energyCostPerDamageUnit;

        // Calculate how many segments this represents
        float actualEnergyPerSegment = GetActualEnergyPerSegment();
        float segmentsNeeded = energyCostToAbsorb / actualEnergyPerSegment;

        if (debugFallDamage)
        {
            Debug.Log($"[FallDamageSystem] Fall damage calculation - Height: {fallHeight}m, Velocity: {fallVelocity}m/s, " +
                     $"Combined danger: {combinedDanger:F3}, Damage units: {damageUnits:F1}, " +
                     $"Energy cost: {energyCostToAbsorb:F0}, Actual energy/segment: {actualEnergyPerSegment:F0}, Segments needed: {segmentsNeeded:F1}");
        }

        // Apply damage based on energy buffer system
        bool energyAbsorbedDamage = false;
        bool shouldTumble = false;

        if (energyCostToAbsorb > 0)
        {
            if (playerStatus.currentEnergy >= energyCostToAbsorb)
            {
                // Energy can fully absorb this fall damage - drain energy instead of hurting player
                ApplyEnergyDrain(energyCostToAbsorb);
                energyAbsorbedDamage = true;

                // Check if we should tumble due to high energy loss (3+ segments)
                if (segmentsNeeded >= tumbleThresholdSegments)
                {
                    shouldTumble = true;
                    if (debugFallDamage)
                    {
                        Debug.Log($"[FallDamageSystem] High energy loss ({segmentsNeeded:F1} segments >= {tumbleThresholdSegments}) - tumble will occur despite energy absorption");
                    }
                }

                if (debugFallDamage)
                {
                    Debug.Log($"[FallDamageSystem] Energy absorbed fall damage ({energyCostToAbsorb:F0} energy, ~{segmentsNeeded:F1} segments @ {actualEnergyPerSegment:F0}/segment) - " +
                             $"Height: {fallHeight}m, Velocity: {fallVelocity}m/s, Remaining energy: {playerStatus.currentEnergy}");
                }
            }
            else
            {
                // Energy buffer insufficient - drain remaining energy and apply tumble or death
                float remainingEnergy = playerStatus.currentEnergy;
                if (remainingEnergy > 0)
                {
                    ApplyEnergyDrain(remainingEnergy); // Drain all remaining energy first
                }

                // Check for lethal velocity first - if velocity is extreme, force death regardless of energy cost
                if (fallVelocity >= lethalFallVelocity)
                {
                    if (debugFallDamage)
                    {
                        Debug.Log($"[FallDamageSystem] Fatal fall - Lethal velocity reached after energy drain: {fallVelocity}m/s >= {lethalFallVelocity}m/s");
                    }
                    HandleFatalFall();
                    return false; // Player died
                }

                // Determine what happens when energy buffer is insufficient
                // Only truly massive falls (requiring many segments) should cause death
                if (segmentsNeeded >= deathThresholdSegments) // Extremely high energy cost = death
                {
                    ApplyActualFallDamage(combinedDanger, fallHeight, fallVelocity);
                }
                else // Lower energy cost = tumble
                {
                    shouldTumble = true;
                    if (debugFallDamage)
                    {
                        Debug.Log($"[FallDamageSystem] Energy buffer insufficient but fall not lethal ({segmentsNeeded:F1} segments < {deathThresholdSegments}) - will tumble instead of dying");
                    }
                }

                if (debugFallDamage)
                {
                    Debug.Log($"[FallDamageSystem] Energy buffer insufficient ({remainingEnergy:F0} < {energyCostToAbsorb:F0}) - " +
                             $"Danger: {combinedDanger:F3}, Height: {fallHeight}m, Velocity: {fallVelocity}m/s");
                }
            }

            // Notify subscribers
            OnFallDamageApplied?.Invoke();
        }
        else if (debugFallDamage)
        {
            Debug.Log($"[FallDamageSystem] Minor fall detected but no damage taken - " +
                     $"Height: {fallHeight}m, Velocity: {fallVelocity}m/s, Danger: {combinedDanger}");
        }

        // If energy absorbed damage and we should tumble, force the tumble directly
        if (energyAbsorbedDamage && shouldTumble)
        {
            if (debugFallDamage)
            {
                Debug.Log($"[FallDamageSystem] Energy absorbed damage but tumble required - forcing tumble directly");
            }
            CheckForTumbleRequired(true); // Force tumble due to energy system decision
            return true; // Prevent normal tumble check since we handled it
        }
        
        // Store tumble decision for CheckForTumbleRequired to use
        return energyAbsorbedDamage && !shouldTumble; // Only prevent normal tumble if energy absorbed AND we don't need to tumble
    }

    private void ApplyEnergyDrain(float energyDamage)
    {
        if (playerStatus == null) return;

        // Drain energy for fall protection
        playerStatus.DrainEnergy(energyDamage);
    }

    private void ApplyActualFallDamage(float dangerLevel, float fallHeight, float fallVelocity)
    {
        if (playerStatus == null) return;

        // Apply actual fall damage to the player based on danger level
        string damageDescription = dangerLevel >= 0.9f ? "lethal" : dangerLevel >= 0.7f ? "severe" : "moderate";

        if (debugFallDamage)
        {
            Debug.Log($"[FallDamageSystem] Applying {damageDescription} fall damage to player (danger: {dangerLevel:F3}) - " +
                     $"Height: {fallHeight}m, Velocity: {fallVelocity}m/s");
        }

        // For now, any actual fall damage kills the player
        // In the future, you could implement different damage amounts based on dangerLevel:
        // - dangerLevel < 0.5: Light damage (reduce health)
        // - dangerLevel 0.5-0.7: Moderate damage (reduce health significantly)
        // - dangerLevel 0.7-0.9: Severe damage (reduce health critically or kill)
        // - dangerLevel >= 0.9: Lethal damage (instant death)

        Debug.Log($"[FallDamageSystem] Player takes {damageDescription} fall damage - triggering death");

        // Call the death manager
        DeathManager deathManager = DeathManager.Instance;
        if (deathManager != null)
        {
            deathManager.HandlePlayerDeath();
        }
        else
        {
            Debug.LogError("[FallDamageSystem] DeathManager not found! Player should have died from fall damage.");
        }
    }

    private void HandleFatalFall()
    {
        OnFatalFall?.Invoke();

        if (playerStatus != null)
        {
            // Fatal falls kill the player regardless of energy protection
            if (playerStatus.currentEnergy > 0)
            {
                // Drain all energy first as visual feedback
                playerStatus.DrainEnergy(playerStatus.currentEnergy);
                Debug.Log("[FallDamageSystem] Fatal fall - all energy drained, triggering death");
            }
            else
            {
                Debug.Log("[FallDamageSystem] Fatal fall with no energy protection - triggering death");
            }
            
            // Player dies from fatal fall
            DeathManager deathManager = DeathManager.Instance;
            if (deathManager != null)
            {
                deathManager.HandlePlayerDeath();
            }
            else
            {
                Debug.LogError("[FallDamageSystem] DeathManager not found! Player should have died from fatal fall.");
            }
        }
    }
    #endregion

    #region Public Interface
    // Added for PlayerDebugDisplay
    public float GetCurrentFallHeight()
    {
        if (isFalling)
        {
            return fallStartY - transform.position.y;
        }
        return 0f;
    }

    public float GetLastFallDistance()
    {
        return lastFallDistance;
    }

    public float GetLastImpactSpeed()
    {
        return lastImpactSpeed;
    }

    public bool IsFalling()
    {
        return isFalling;
    }

    public bool IsCurrentFallDeadly()
    {
        if (!isFalling || !fallDamageEnabled)
            return false;

        if (characterMotor == null)
            return false;

        // Require a minimum fall time to avoid false positives during wall running, etc.
        if ((Time.time - fallStartTime) < minFallTimeToEnableDamage)
            return false;

        float fallDistance = fallStartY - transform.position.y;

        // Ignore small falls
        if (fallDistance < 1.0f)
            return false;

        // Get current velocities
        float verticalVelocity = Mathf.Abs(characterMotor.BaseVelocity.y);
        float horizontalSpeed = new Vector3(characterMotor.BaseVelocity.x, 0, characterMotor.BaseVelocity.z).magnitude;

        // Check if fall velocity exceeds lethal threshold
        if (verticalVelocity >= lethalFallVelocity)
        {
            if (debugFallDamage)
                Debug.Log($"[FallDamageSystem] Deadly fall detected - Fall velocity: {verticalVelocity}m/s >= {lethalFallVelocity}m/s");
            return true;
        }

        // Check if height exceeds lethal threshold
        if (fallDistance >= lethalFallHeight)
        {
            if (debugFallDamage)
                Debug.Log($"[FallDamageSystem] Deadly fall detected - Height: {fallDistance}m >= {lethalFallHeight}m");
            return true;
        }

        // Check combined danger
        float heightDanger = Mathf.InverseLerp(minFallDamageHeight, lethalFallHeight, fallDistance);
        float velocityDanger = Mathf.InverseLerp(minFallDamageVelocity, lethalFallVelocity, verticalVelocity);
        float horizontalDanger = Mathf.InverseLerp(5f, 15f, horizontalSpeed) * horizontalSpeedFactor;
        float combinedDanger = (heightDanger * fallHeightWeight) + (velocityDanger * fallVelocityWeight) + horizontalDanger;

        // Need a higher threshold for prediction
        if (combinedDanger >= 0.75f)
        {
            if (debugFallDamage)
                Debug.Log($"[FallDamageSystem] Deadly fall detected - Height: {fallDistance}m, " +
                          $"Fall Velocity: {verticalVelocity}m/s, Horizontal: {horizontalSpeed}m/s, Danger: {combinedDanger}");
            return true;
        }

        return false;
    }

    public bool ForceApplyFallDamage(float height, float speed)
    {
        return ApplyFallDamage(height, 0f, speed);
    }

    // Simple method to start fall tracking from a specified height (for save/load continuity)
    public void StartFallTrackingFromHeight(float startHeight)
    {
        isFalling = true;
        fallStartY = startHeight;
        fallStartTime = Time.time;
        maxFallVelocity = characterMotor != null ? Mathf.Abs(characterMotor.BaseVelocity.y) : 0f;
        deadlyFallWarningFired = false;
        previousFrameYPosition = transform.position.y;

        if (debugFallDamage)
        {
            Debug.Log($"[FallDamageSystem] Started fall tracking from saved height: {startHeight}m, current velocity: {maxFallVelocity}m/s");
        }
    }

    // Simple getter for fall start height
    public float GetFallStartHeight()
    {
        return fallStartY;
    }
    #endregion
} 