using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Inventory;
using System;

public class InvItemPickup : MonoBehaviour, IInteractable
{
    public float InteractionDistance => 2f;
    public float pickupDelay = 0.5f;
    private float timeSinceThrown;
    private bool isBeingDestroyed = false;

    // Item properties
    public int quantity = 1;
    public Item item;

    // References
    private EquipmentManager equipmentManager;
    private InvUI invUI;
    private PlayerStatus playerStatus;
    private PlayerProgressionManager progressionManager;

    // Integrated highlighting system (from SimpleHighlighter)
    private List<GameObject> targetObjects = new List<GameObject>();
    private Dictionary<GameObject, int> originalLayers = new Dictionary<GameObject, int>();
    private bool isHighlighted = false;
    private bool highlightingInitialized = false;
    
    // Items layer is layer 10
    private const int ITEMS_LAYER = 10;

    // For physics grabbing
    private Rigidbody rb;

    // To track manual movement
    private Vector3 lastPosition;
    private Quaternion lastRotation;
    private float positionCheckTimer = 0f;
    private const float POSITION_CHECK_INTERVAL = 10.0f; // Increased from 2.0f to 10.0f to significantly reduce check frequency
    private bool hasMoved = false; // Track whether the item has been moved since spawn
    private bool wasPositionUpdated = false; // Track if we already updated position this session
    private Transform cachedTransform;

    private void Awake()
    {
        // Initialize highlighting system
        InitializeHighlighting();

        // Setup rigidbody for physics grabbing
        rb = GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = gameObject.AddComponent<Rigidbody>();
            rb.mass = 1f;
            rb.linearDamping = 0.5f;
            rb.angularDamping = 0.5f;
            rb.interpolation = RigidbodyInterpolation.Interpolate;
            rb.collisionDetectionMode = CollisionDetectionMode.Continuous;
        }

        // Store initial position for movement tracking
        lastPosition = transform.position;
        lastRotation = transform.rotation;

        // Cache transform for better performance with large number of objects
        cachedTransform = transform;
    }

    /// <summary>
    /// Initializes the highlighting system by caching all game objects and their original layers
    /// </summary>
    private void InitializeHighlighting()
    {
        if (highlightingInitialized)
            return;

        targetObjects.Clear();
        originalLayers.Clear();
        
        // Get this object and all children
        Transform[] allTransforms = GetComponentsInChildren<Transform>(true);
        
        foreach (Transform transform in allTransforms)
        {
            GameObject obj = transform.gameObject;
            targetObjects.Add(obj);
            originalLayers[obj] = obj.layer;
        }
        
        highlightingInitialized = true;
    }

    private void Start()
    {
        equipmentManager = FindObjectOfType<EquipmentManager>();
        invUI = FindObjectOfType<InvUI>();
        playerStatus = FindObjectOfType<PlayerStatus>();
        progressionManager = PlayerProgressionManager.Instance;

        if (invUI == null)
        {
            Debug.LogError("InvUI component not found in the scene!");
        }

        // Make sure we have an ItemUniqueId component for persistence
        var idComponent = GetComponent<ItemUniqueId>();
        if (idComponent == null)
        {
            idComponent = gameObject.AddComponent<ItemUniqueId>();
            // ID will be assigned by progression manager when registered
        }
        else if (hasMoved && !idComponent.WasPlayerDropped)
        {
            // If this item was already moved in a previous session, make sure we mark it as stabilized
            idComponent.WasStabilized = true;
        }

        // For bag items, ensure they have an InvDroppedStorageEquipment component
        if (item is Bag)
        {
            bool isDevPlaced = idComponent != null && !idComponent.WasPlayerDropped;
            var storageComponent = GetComponent<InvDroppedStorageEquipment>();
            
            if (storageComponent == null)
            {
                storageComponent = gameObject.AddComponent<InvDroppedStorageEquipment>();
                
                // Always initialize with empty content for dev-placed bags
                storageComponent.SetContainerSnapshot("{}");
                Debug.Log($"[InvItemPickup] Added storage component to bag {item.itemName} with empty content");
            }
            else if (string.IsNullOrEmpty(storageComponent.GetContainerSnapshot()) || isDevPlaced)
            {
                // If component exists but has no content, or this is a dev-placed bag, set empty JSON object
                storageComponent.SetContainerSnapshot("{}");
                
                if (isDevPlaced)
                {
                    Debug.Log($"[InvItemPickup] Force set empty content for dev-placed bag {item.itemName}");
                }
                else
                {
                    Debug.Log($"[InvItemPickup] Set empty content for bag with no content data");
                }
            }
        }

        // Initialize the position tracking variables to properly detect movement
        lastPosition = transform.position;
        lastRotation = transform.rotation;

        // If the transform position is different from what's in ItemUniqueId, mark it as moved
        // This helps when the position was changed in the editor or via script
        if (idComponent != null && idComponent.InitialPosition != Vector3.zero)
        {
            // Check if current position is different from initial position by a significant amount
            // Increased threshold to 5 meters to match our other change and avoid false positives
            float distance = Vector3.Distance(transform.position, idComponent.InitialPosition);
            if (distance > 5.0f)
            {
                hasMoved = true;
                idComponent.WasStabilized = true;
                
                // Update the stored position to avoid triggering a false position change on the next check
                idComponent.InitialPosition = transform.position;
                
                Debug.Log($"[InvItemPickup] {gameObject.name} detected as significantly moved (initial: {idComponent.InitialPosition}, current: {transform.position}, distance: {distance:F2}m)");
            }
            else
            {
                // If the position changed is minor (< 5m), just update without logging
                idComponent.InitialPosition = transform.position;
            }
        }
        else if (idComponent != null)
        {
            // Store the initial position for future reference
            idComponent.InitialPosition = transform.position;
        }

        // Register this item with the progression manager
        if (progressionManager != null && item != null)
        {
            // Wait one frame to ensure all components are initialized
            StartCoroutine(RegisterAfterFrame());
        }
        else
        {
            if (progressionManager == null)
                Debug.LogWarning($"[InvItemPickup] {gameObject.name}: No ProgressionManager found! Item persistence will not work.");
            if (item == null)
                Debug.LogWarning($"[InvItemPickup] {gameObject.name}: No Item assigned! Can't register for persistence.");
        }
    }

    private System.Collections.IEnumerator RegisterAfterFrame()
    {
        yield return null;

        // Store the current position before registration
        Vector3 positionBeforeRegister = transform.position;
        Quaternion rotationBeforeRegister = transform.rotation;

        // Register with progression manager
        progressionManager.RegisterWorldItem(this);

        // Ensure position doesn't change during registration
        transform.position = positionBeforeRegister;
        transform.rotation = rotationBeforeRegister;

        // Update the lastPosition/lastRotation to match current position
        lastPosition = transform.position;
        lastRotation = transform.rotation;
    }

    private void Update()
    {
        // Update time since thrown to prevent immediately picking up thrown items
        if (timeSinceThrown < pickupDelay)
        {
            timeSinceThrown += Time.deltaTime;
        }

        // Check if position has changed significantly since last check
        positionCheckTimer += Time.deltaTime;
        if (positionCheckTimer >= POSITION_CHECK_INTERVAL)
        {
            CheckForMovement();
            positionCheckTimer = 0f;
        }
    }

    // Check if the item has been moved manually and update persistence if needed
    private void CheckForMovement()
    {
        // If position or rotation changed significantly, update the registered position
        float positionDifference = Vector3.Distance(transform.position, lastPosition);
        float rotationDifference = Quaternion.Angle(transform.rotation, lastRotation);

        // Check if moved more than 1cm or rotated more than 1 degree
        if (positionDifference > 0.01f || rotationDifference > 1f)
        {
            // Update saved position
            lastPosition = transform.position;
            lastRotation = transform.rotation;
            hasMoved = true; // Mark as moved

            // Ensure the ID component exists and is properly configured
            EnsureStableID();

            // Notify progression manager to update saved position (but not too frequently)
            if (progressionManager != null && item != null)
            {
                // For dev-placed items, make sure we always update position after first move
                var idComponent = GetComponent<ItemUniqueId>();
                bool isDevPlacedItem = idComponent != null && !idComponent.WasPlayerDropped;
                
                // Only consider a significant position change if moved more than 5 meters (increased from 0.5m)
                bool isSignificantMove = positionDifference > 5.0f;
                bool shouldForceSave = isDevPlacedItem || isSignificantMove || !wasPositionUpdated;

                // Make sure developer-placed items are properly stabilized when moved
                if (isDevPlacedItem && !idComponent.WasStabilized)
                {
                    idComponent.WasStabilized = true;
                    // Only log the first time a dev item is stabilized
                    if (!wasPositionUpdated)
                    {
                        Debug.Log($"[InvItemPickup] Developer-placed item {gameObject.name} marked as stabilized after movement");
                    }
                }

                // Register all movement with the progression manager
                progressionManager.RegisterWorldItem(this);
                wasPositionUpdated = true;

                // Force an immediate save if:
                // 1. The change is significant (moved more than 5 meters), or
                // 2. It's a developer-placed item being moved, or
                // 3. This is the first detected movement
                if (shouldForceSave)
                {
                    progressionManager.SaveProgressionData();

                    // Only log significant movements (> 5m)
                    if (isSignificantMove)
                    {
                        Debug.Log($"[InvItemPickup] Significant position change detected for {item.itemName} ({positionDifference:F2}m), forcing save");
                    }
                    else if (isDevPlacedItem && !wasPositionUpdated)
                    {
                        Debug.Log($"[InvItemPickup] Developer item {item.itemName} moved for first time, forcing save");
                    }
                }
            }
        }
    }

    // Ensure this item has a stable ID that will persist even when moved
    private void EnsureStableID()
    {
        var idComponent = GetComponent<ItemUniqueId>();
        if (idComponent != null)
        {
            // If this item has moved, we need to make sure it keeps its ID
            if (hasMoved && !idComponent.WasPlayerDropped && !idComponent.WasStabilized)
            {
                // Mark that this ID should persist for a moved item
                idComponent.WasStabilized = true;

                // Make sure the progress manager knows about it
                if (progressionManager != null)
                {
                    progressionManager.RegisterWorldItem(this);
                    // Only log once when an item is first stabilized
                    Debug.Log($"[InvItemPickup] Item {gameObject.name} marked as stabilized");
                }
            }
            // If already stabilized, just register without logging
            else if (hasMoved && !idComponent.WasPlayerDropped && progressionManager != null)
            {
                progressionManager.RegisterWorldItem(this);
            }
        }
    }

    private void UpdateUI()
    {
        if (invUI == null)
        {
            Debug.LogError("InvUI component not found in the scene!");
        }
        invUI?.UpdateUI();
    }

    // You can set the item through the inspector or in code
    public void SetItem(Item newItem, int newQuantity)
    {
        item = newItem;
        quantity = newQuantity;

        // No longer using model swapper - comment out or remove this code
        // var modelSwapper = GetComponent<InvItemModelSwapper>();
        // if (modelSwapper != null)
        // {
        //     modelSwapper.SetupItemModel();
        // }

        // If this is called after Start(), re-register with progression manager
        if (progressionManager != null && isActiveAndEnabled)
        {
            progressionManager.RegisterWorldItem(this);
        }
    }

    public bool CanInteract(GameObject interactor) => !isBeingDestroyed && timeSinceThrown >= pickupDelay;

    public void Interact(GameObject interactor)
    {
        if (isBeingDestroyed) return;

        Debug.Log($"[InvItemPickup] Handling pickup for {interactor.name}");

        // First try to handle the pickup operation
        bool successful = HandlePickup(interactor);

        // Only notify the progression manager if the pickup was successful
        if (successful)
        {
            // Notify the progression manager about the successful pickup
            if (progressionManager != null)
            {
                progressionManager.OnItemPickedUp(this);
                Debug.Log($"[InvItemPickup] Notified progression manager about pickup of {item?.itemName}");
            }
            else
            {
                Debug.LogWarning("[InvItemPickup] ProgressionManager not found! Item persistence may not work correctly.");
                // Try one more time to find it
                progressionManager = PlayerProgressionManager.Instance;
                if (progressionManager != null)
                {
                    progressionManager.OnItemPickedUp(this);
                }
            }
            
            DestroyItem();
        }
        else
        {
            // If picking up failed (e.g., inventory full), we just log and the item stays in the world
            Debug.Log("[InvItemPickup] Pickup failed, item remains in world");
        }
    }

    public bool HandlePickup(GameObject interactor)
    {
        EquipmentManager equipmentManager = interactor.GetComponent<EquipmentManager>();
        if (equipmentManager == null) return false;

        bool itemHandled = false;

        if (item is EquipmentBase equipItem)
        {
            itemHandled = HandleEquipmentPickup(equipmentManager, equipItem);
            Debug.Log($"[InvItemPickup] Equipment pickup result: {itemHandled}");
        }
        else
        {
            // First try stacking with existing items if not multi-slot
            if (!item.IsMultiSlot)
            {
                itemHandled = TryStackWithExisting(equipmentManager);
                Debug.Log($"[InvItemPickup] Stack attempt result: {itemHandled}");
            }

            // Try containers
            if (!itemHandled)
            {
                itemHandled = TryAddToContainers(equipmentManager);
                Debug.Log($"[InvItemPickup] Container attempt result: {itemHandled}");
            }
        }

        if (itemHandled)
        {
            equipmentManager.ForceImmediateUIUpdate();
            UpdateUI();

            // Show notification when item is picked up
            if (NotificationManager.Instance != null)
            {
                string notificationMessage = $"Picked up {quantity}x {item.itemName}";
                NotificationManager.Instance.ShowNotification(notificationMessage);
            }

            // Notify the ToolSelectionManager about the new item
            var toolSelectionManager = UnityEngine.Object.FindObjectOfType<ToolSelectionManager>();
            if (toolSelectionManager != null)
            {
                // Check if the item is a tool and update the tool selection
                toolSelectionManager.CheckNewItem(item, true);
            }
        }
        else
        {
            // Show notification when item can't be picked up
            if (NotificationManager.Instance != null)
            {
                string notificationMessage = $"No space to pick up {item.itemName}";
                NotificationManager.Instance.ShowNotification(notificationMessage);
            }
        }

        return itemHandled;
    }

    private void DestroyItem()
    {
        isBeingDestroyed = true;
        Destroy(gameObject);
    }

    private bool HandleEquipmentPickup(EquipmentManager equipmentManager, EquipmentBase equipment)
    {
        if (equipment is Bag)
        {
            Debug.Log($"[InvItemPickup] Handling bag pickup for {equipment.itemName}");
            return HandleStorageItemPickup(equipmentManager, equipment);
        }
        else
        {
            Debug.Log($"[InvItemPickup] Handling regular equipment pickup for {equipment.itemName}");
            return equipmentManager.EquipItem(equipment);
        }
    }

    private bool HandleStorageItemPickup(EquipmentManager equipmentManager, EquipmentBase storageItem)
    {
        var slot = equipmentManager.GetEquipmentSlot(storageItem.Slot);

        // Check if we're dealing with a bag
        if (storageItem is Bag)
        {
            // Get the unique ID component to check if this is a dev-placed bag
            var idComponent = GetComponent<ItemUniqueId>();
            bool isDevPlaced = idComponent != null && !idComponent.WasPlayerDropped;
            
            // Get the container snapshot to determine if the bag is empty
            InvDroppedStorageEquipment droppedStorage = GetComponent<InvDroppedStorageEquipment>();
            
            // For dev-placed bags, always treat as empty
            bool isEmptyBag = isDevPlaced;
            
            if (!isDevPlaced && droppedStorage != null)
            {
                // Use the new helper method for better empty detection
                isEmptyBag = droppedStorage.IsContainerEmpty();
            }
            else if (!isDevPlaced)
            {
                // No storage component, treat as empty
                isEmptyBag = true;
                Debug.Log($"[InvItemPickup] No storage component found, treating bag as empty");
            }
            
            if (isDevPlaced)
            {
                Debug.Log($"[InvItemPickup] Developer-placed bag, always treating as empty");
            }

            Debug.Log($"[InvItemPickup] Is bag empty? {isEmptyBag}");

            // If the bag is empty, we should also try to add it to existing container slots
            if (isEmptyBag)
            {
                // Try to add the empty bag to a container
                bool addedToContainer = TryAddToContainers(equipmentManager);
                if (addedToContainer)
                {
                    Debug.Log($"[InvItemPickup] Empty bag added to container");
                    return true;
                }
                
                // Even if we can't add to containers, we should still allow equipping if the slot is empty
                // This section runs if TryAddToContainers failed but the bag is empty
            }
            else if (slot != null && slot.equippedItem != null)
            {
                // Only if the bag contains items AND there's already a bag equipped, don't allow pickup
                Debug.Log($"[InvItemPickup] Cannot pick up non-empty bag when another is equipped");
                return false;
            }
        }

        // Original code for equipping the item
        if (slot != null && slot.equippedItem == null)
        {
            bool equipped = equipmentManager.EquipItem(storageItem);
            if (equipped)
            {
                // Check if this is a dev-placed bag 
                var idComponent = GetComponent<ItemUniqueId>();
                bool isDevPlaced = idComponent != null && !idComponent.WasPlayerDropped;
                
                // Always create a new storage container for the bag when it's equipped
                if (storageItem is Bag bag)
                {
                    var (width, height) = GetStorageGridDimensions(storageItem);
                    
                    // For dev-placed bags, always create a fresh empty container
                    if (isDevPlaced)
                    {
                        // Force a new empty container for dev-placed bags
                        slot.storageContainer = new InvItemContainer(width, height);
                        Debug.Log($"[InvItemPickup] Created new empty container for dev-placed bag {storageItem.itemName}");
                    }
                    else
                    {
                        // For player-dropped bags, restore the content from serialization
                        InvDroppedStorageEquipment droppedStorage = GetComponent<InvDroppedStorageEquipment>();
                        if (droppedStorage != null)
                        {
                            string serializedContent = droppedStorage.GetContainerSnapshot();
                            
                            Debug.Log($"[InvItemPickup] Retrieved snapshot: {serializedContent}");
                            if (!string.IsNullOrEmpty(serializedContent) && serializedContent != "{}")
                            {
                                // Create a new container before populating it
                                slot.storageContainer = new InvItemContainer(width, height);
                                
                                try
                                {
                                    var itemList = JsonUtility.FromJson<SerializableItemList>(serializedContent);
                                    if (itemList?.items != null)
                                    {
                                        foreach (var serializableItem in itemList.items)
                                        {
                                            if (!string.IsNullOrEmpty(serializableItem.itemName))
                                            {
                                                Item item = ItemDatabase.GetItemByName(serializableItem.itemName);
                                                if (item != null)
                                                {
                                                    slot.storageContainer.AddItemToSlot(
                                                        item,
                                                        serializableItem.quantity,
                                                        serializableItem.gridPosition,
                                                        serializableItem.isRotated
                                                    );
                                                }
                                            }
                                        }
                                    }
                                }
                                catch (System.Exception e)
                                {
                                    Debug.LogError($"Error deserializing storage content: {e.Message}");
                                }
                            }
                            else
                            {
                                // Empty player-dropped bag, create empty container
                                slot.storageContainer = new InvItemContainer(width, height);
                                Debug.Log($"[InvItemPickup] Created new empty container for empty player-dropped bag");
                            }
                        }
                        else
                        {
                            // No storage component but still need a container
                            slot.storageContainer = new InvItemContainer(width, height);
                            Debug.Log($"[InvItemPickup] Created new container (no storage component found)");
                        }
                    }
                }
                
                equipmentManager.ForceImmediateUIUpdate();
                return true;
            }
        }
        return false;
    }

    private (int width, int height) GetStorageGridDimensions(EquipmentBase equipment)
    {
        return equipment switch
        {
            Bag bag => (bag.GridWidth, bag.GridHeight),
            _ => (0, 0)
        };
    }

    private bool TryStackWithExisting(EquipmentManager equipmentManager)
    {
        int remainingQuantity = quantity;

        // Try containers
        var storageEquipments = GetStorageContainers(equipmentManager);
        foreach (var storageSlot in storageEquipments)
        {
            if (storageSlot?.storageContainer != null)
            {
                var existingStacks = storageSlot.storageContainer.GetItems()
                    .Where(s => s.Item == item && s.Quantity < item.maxStack)
                    .OrderBy(s => s.Quantity)  // Try to fill up stacks from lowest to highest
                    .ToList();

                foreach (var stack in existingStacks)
                {
                    if (remainingQuantity <= 0) break;

                    if (storageSlot.storageContainer.TryGetItemPosition(stack, out Vector2Int pos, out bool isRotated))
                    {
                        int spaceInStack = item.maxStack - stack.Quantity;
                        int amountToAdd = Mathf.Min(remainingQuantity, spaceInStack);

                        if (amountToAdd > 0)
                        {
                            int index = pos.y * storageSlot.storageContainer.GridWidth + pos.x;
                            equipmentManager.RemoveItemFromContainer(storageSlot.slotType, index);
                            equipmentManager.AddItemToContainerSlot(storageSlot.slotType, item, stack.Quantity + amountToAdd, index, isRotated);
                            remainingQuantity -= amountToAdd;

                            if (remainingQuantity <= 0)
                                return true;
                        }
                    }
                }
            }
        }

        // If we still have remaining quantity, but stacked some
        if (remainingQuantity > 0 && remainingQuantity < quantity)
        {
            // Update quantity for container attempts
            quantity = remainingQuantity;
            return false; // Let the calling method try other methods with remaining quantity
        }

        return remainingQuantity != quantity; // Return true if we stacked anything
    }

    private bool TryAddToContainers(EquipmentManager equipmentManager)
    {
        // Try containers
        var storageEquipments = GetStorageContainers(equipmentManager);
        foreach (var storageSlot in storageEquipments)
        {
            if (storageSlot?.storageContainer == null) continue;

            Vector2Int? position = storageSlot.storageContainer.FindSpaceForItem(item, true);
            if (position.HasValue)
            {
                int index = position.Value.y * storageSlot.storageContainer.GridWidth + position.Value.x;
                if (equipmentManager.AddItemToContainerSlot(storageSlot.slotType, item, quantity, index))
                {
                    // If this is a bag with content being added to another container,
                    // we need to clear its content only if it has items
                    if (item is Bag)
                    {
                        InvDroppedStorageEquipment droppedStorage = GetComponent<InvDroppedStorageEquipment>();
                        if (droppedStorage != null)
                        {
                            string serializedContent = droppedStorage.GetContainerSnapshot();
                            bool hasContent = !string.IsNullOrEmpty(serializedContent) && serializedContent != "{}";

                            if (hasContent)
                            {
                                // Only empty its content when stored in a container if it actually has content
                                droppedStorage.SetContainerSnapshot("{}");
                                Debug.Log($"[InvItemPickup] Emptied bag content when storing in container");
                            }
                            else
                            {
                                Debug.Log($"[InvItemPickup] Bag was already empty, no need to clear content");
                            }
                        }
                    }
                    return true;
                }
            }
        }
        return false;
    }

    private List<EquipmentManager.EquipmentSlot> GetStorageContainers(EquipmentManager equipmentManager)
    {
        var slots = new List<EquipmentManager.EquipmentSlot>();
        foreach (var slotType in System.Enum.GetValues(typeof(EquipmentSlotType)).Cast<EquipmentSlotType>())
        {
            var slot = equipmentManager.GetEquipmentSlot(slotType);
            if (slot != null && slot.equippedItem != null &&
                slot.equippedItem is Bag &&
                slot.storageContainer != null)
            {
                slots.Add(slot);
            }
        }
        return slots;
    }

    public string GetInteractionPrompt() => "Press F to pick up or hold F to grab " + (item != null ? item.itemName : "???");

    public void Highlight()
    {
        if (!isHighlighted)
        {
            // Initialize highlighting if not already done
            if (!highlightingInitialized)
            {
                InitializeHighlighting();
            }
            
            isHighlighted = true;
            
            foreach (GameObject obj in targetObjects)
            {
                if (obj == null) continue;
                obj.layer = ITEMS_LAYER;
            }
        }
    }

    public void RemoveHighlight()
    {
        if (isHighlighted)
        {
            isHighlighted = false;
            
            foreach (GameObject obj in targetObjects)
            {
                if (obj == null) continue;
                
                if (originalLayers.TryGetValue(obj, out int originalLayer))
                {
                    obj.layer = originalLayer;
                }
            }
        }
    }

    public void SetTimeSinceThrown(float time)
    {
        timeSinceThrown = time;
    }

    /// <summary>
    /// Used to mark an item as dropped by the player for persistence
    /// </summary>
    public void MarkAsPlayerDropped()
    {
        // Store current position and rotation
        Vector3 currentPosition = transform.position;
        Quaternion currentRotation = transform.rotation;

        var idComponent = GetComponent<ItemUniqueId>();
        if (idComponent == null)
        {
            idComponent = gameObject.AddComponent<ItemUniqueId>();
            idComponent.UniqueId = System.Guid.NewGuid().ToString();
            Debug.Log($"[InvItemPickup] Generated new GUID for player-dropped item: {idComponent.UniqueId}");
        }

        idComponent.WasPlayerDropped = true;
        idComponent.WasStabilized = true; // Always stabilize player-dropped items
        idComponent.InitialPosition = currentPosition; // Store the initial position

        // Update the tracking variables
        lastPosition = currentPosition;
        lastRotation = currentRotation;
        hasMoved = true; // Mark as moved immediately

        // Notify the progression manager about this dropped item
        if (progressionManager == null)
        {
            progressionManager = PlayerProgressionManager.Instance;
        }

        if (progressionManager != null)
        {
            progressionManager.OnItemDropped(this, true);
        }
        else
        {
            Debug.LogError("[InvItemPickup] Cannot mark item as dropped - ProgressionManager not found!");
        }
    }

    // Debug method to test bag functionality
    private void DebugBagDatabase()
    {
        if (item == null)
        {
            Debug.LogError("[InvItemPickup] Item is null!");
            return;
        }

        Debug.Log($"[InvItemPickup] Current item: {item.name}, Type: {item.GetType().Name}");

        if (item is Bag bag)
        {
            Debug.Log($"[InvItemPickup] Bag details - GridWidth: {bag.GridWidth}, GridHeight: {bag.GridHeight}, Slot: {bag.Slot}");
        }

        // Debug database functionality if available
        try
        {
            // Check if the item exists in resources
            Bag[] bagResources = Resources.FindObjectsOfTypeAll<Bag>();
            Debug.Log($"[InvItemPickup] Found {bagResources.Length} bags in Resources");

            foreach (var bagResource in bagResources)
            {
                Debug.Log($"[InvItemPickup] Resource bag: {bagResource.name}, Slot: {bagResource.Slot}");
            }

            // Try to find this specific item in resources
            if (item is EquipmentBase equipment)
            {
                Debug.Log($"[InvItemPickup] Looking for item with slot: {equipment.Slot}");

                EquipmentBase[] equipmentResources = Resources.FindObjectsOfTypeAll<EquipmentBase>();
                foreach (var eq in equipmentResources)
                {
                    if (eq.Slot == equipment.Slot)
                    {
                        Debug.Log($"[InvItemPickup] Found equipment with matching slot: {eq.name}");
                    }
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"[InvItemPickup] Error checking resources: {e.Message}");
        }
    }

    private void OnDestroy()
    {
        // Clean up highlighting if active
        if (isHighlighted)
        {
            RemoveHighlight();
        }

        // If this item has moved, make sure its final position is saved before destruction
        if ((hasMoved || wasPositionUpdated) && progressionManager != null && item != null)
        {
            try
            {
                progressionManager.RegisterWorldItem(this);
                progressionManager.SaveProgressionData();
                Debug.Log($"[InvItemPickup] Final position saved for {item.itemName} before destruction");
            }
            catch (Exception ex)
            {
                Debug.LogError($"[InvItemPickup] Error saving final position: {ex.Message}");
            }
        }
    }

    // Public getter for the hasMoved property to avoid using reflection
    public bool HasMoved()
    {
        return hasMoved;
    }

    // Public setter for the hasMoved property
    public void SetHasMoved(bool moved)
    {
        hasMoved = moved;

        // If we're marking as moved, ensure we have a stable ID
        if (moved)
        {
            EnsureStableID();
        }
    }
}