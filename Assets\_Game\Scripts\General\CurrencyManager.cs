using UnityEngine;
using System;
using System.Collections;
using UnityEngine.UIElements;

/// <summary>
/// CurrencyManager - A centralized system for managing in-game currency
/// Acts as a single source of truth for currency operations
/// </summary>
public class CurrencyManager : MonoBehaviour
{
    // Singleton instance
    public static CurrencyManager Instance { get; private set; }

    // Events
    public event Action<int, int> OnCurrencyChanged; // (oldValue, newValue)
    
    // Current currency amount - private to enforce access through methods
    [SerializeField] private int _currentCurrency = 0;
    
    // Public getter for currency
    public int CurrentCurrency => _currentCurrency;
    
    // Initialization flag to prevent duplicate initialization
    private bool initialized = false;
    
    // Backup sources - for recovery
    private const string EMERGENCY_BACKUP_KEY = "emergency_currency_backup";
    private const string CURRENCY_BACKUP_KEY = "player_currency_last";
    
    // Add a timestamp to track when we last notified listeners
    private float lastNotificationTime = 0f;
    private const float NOTIFICATION_THROTTLE_TIME = 0.1f; // 10 notifications per second max
    
    #region Unity Lifecycle
    
    private void Awake()
    {
        // Implement singleton pattern
        if (Instance == null)
        {
            Instance = this;
        }
        else
        {
            Destroy(gameObject);
            return;
        }
    }
    
    private void Start()
    {
        // Initialize currency from persistence system
        StartCoroutine(InitializeCurrencyWithFallbacks());
    }
    
    private void OnApplicationQuit()
    {
        // Make sure we save currency to disk
        SaveCurrencyToDisk();
        
        // Create emergency backup with multiple keys for redundancy
        PlayerPrefs.SetInt(EMERGENCY_BACKUP_KEY, _currentCurrency);
        PlayerPrefs.SetInt(CURRENCY_BACKUP_KEY, _currentCurrency);
        PlayerPrefs.SetInt("player_currency_final", _currentCurrency);
        PlayerPrefs.SetInt("emergency_currency_final", _currentCurrency);
        PlayerPrefs.Save();
        
        // Try to update PlayerProgressionManager as final precaution
        var progressManager = FindObjectOfType<PersistenceManager>();
        if (progressManager != null)
        {
            progressManager.PlayerMoney = _currentCurrency;
            progressManager.SavePlayerCurrency();
        }
    }
    
    private void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus)
        {
            SaveCurrencyToDisk();
        }
    }
    
    #endregion
    
    #region Initialization
    
    private IEnumerator InitializeCurrencyWithFallbacks()
    {
        if (initialized)
            yield break;
            
        initialized = true;
        
        LoadFromBackupSources();
        
        var progressionManager = FindObjectOfType<PersistenceManager>();
        if (progressionManager != null)
        {
            int progressionValue = progressionManager.PlayerMoney;
            if (progressionValue > _currentCurrency)
            {
                _currentCurrency = progressionValue;
            }
            else if (_currentCurrency > progressionValue)
            {
                progressionManager.PlayerMoney = _currentCurrency;
            }
        }
        
        OnCurrencyChanged?.Invoke(_currentCurrency, _currentCurrency);
        
        yield return null;
        yield return null;
        
        NotifyAllListeners();
        
        // Ensure all currency displays are properly formatted
        UpdateAllCurrencyDisplays();
        
        // Additional pass to catch any labels that might have been missed
        yield return new WaitForSeconds(0.5f);
        
        // Find all MoneyCounterAnimation instances and ensure proper formatting
        var moneyAnimations = FindObjectsOfType<MoneyCounterAnimation>();
        foreach (var moneyAnim in moneyAnimations)
        {
            if (moneyAnim != null)
            {
                var uiDocument = moneyAnim.GetComponent<UIDocument>();
                if (uiDocument != null && uiDocument.rootVisualElement != null)
                {
                    var labels = uiDocument.rootVisualElement.Query<Label>().Where(l => 
                        l.text != null && l.text.Contains("€")).ToList();
                        
                    foreach (var label in labels)
                    {
                        if (!label.ClassListContains("shop-item-price") && 
                            label.name != "BuyTotalValue" && 
                            label.name != "SellTotalValue")
                        {
                            moneyAnim.SetColoredCounterValue(label, _currentCurrency);
                        }
                    }
                }
            }
        }
    }

    
    private void LoadFromBackupSources()
    {
        _currentCurrency = GetHighestBackupCurrency();
    }
    
    private int GetHighestBackupCurrency()
    {
        int highestValue = 0;
        
        if (PlayerPrefs.HasKey(EMERGENCY_BACKUP_KEY))
        {
            highestValue = Mathf.Max(highestValue, PlayerPrefs.GetInt(EMERGENCY_BACKUP_KEY, 0));
        }
        
        if (PlayerPrefs.HasKey(CURRENCY_BACKUP_KEY))
        {
            highestValue = Mathf.Max(highestValue, PlayerPrefs.GetInt(CURRENCY_BACKUP_KEY, 0));
        }
        
        if (PlayerPrefs.HasKey("player_currency_final"))
        {
            highestValue = Mathf.Max(highestValue, PlayerPrefs.GetInt("player_currency_final", 0));
        }
        
        if (PlayerPrefs.HasKey("emergency_currency_final"))
        {
            highestValue = Mathf.Max(highestValue, PlayerPrefs.GetInt("emergency_currency_final", 0));
        }
        
        return highestValue;
    }
    
    #endregion
    
    #region Currency Operations
    
    public void AddCurrency(int amount)
    {
        if (amount <= 0)
        {
            Debug.LogWarning($"CurrencyManager: Attempted to add invalid amount: {amount}");
            return;
        }
        
        int oldValue = _currentCurrency;
        _currentCurrency += amount;
        
        OnCurrencyChanged?.Invoke(oldValue, _currentCurrency);
        UpdateAllCurrencyDisplays();
        SaveCurrencyToDisk();
    }
    
    public bool TrySpendCurrency(int amount)
    {
        if (amount <= 0)
        {
            Debug.LogWarning($"CurrencyManager: Attempted to spend invalid amount: {amount}");
            return false;
        }
        
        if (_currentCurrency < amount)
        {
            return false;
        }
        
        int oldValue = _currentCurrency;
        _currentCurrency -= amount;
        
        OnCurrencyChanged?.Invoke(oldValue, _currentCurrency);
        UpdateAllCurrencyDisplays();
        SaveCurrencyToDisk();
        
        return true;
    }
    
    public void SetCurrency(int amount)
    {
        if (amount < 0)
        {
            Debug.LogWarning($"CurrencyManager: Attempted to set negative currency: {amount}");
            return;
        }
        
        int oldValue = _currentCurrency;
        _currentCurrency = amount;
        
        if (oldValue != _currentCurrency)
        {
            OnCurrencyChanged?.Invoke(oldValue, _currentCurrency);
            UpdateAllCurrencyDisplays();
            SaveCurrencyToDisk();
        }
    }
    
    public bool HasSufficientCurrency(int amount)
    {
        return _currentCurrency >= amount;
    }
    
    #endregion
    
    #region Persistence
    
    private void SaveCurrencyToDisk()
    {
        var progressionManager = FindObjectOfType<PersistenceManager>();
        if (progressionManager != null)
        {
            progressionManager.PlayerMoney = _currentCurrency;
        }
        
        PlayerPrefs.SetInt(CURRENCY_BACKUP_KEY, _currentCurrency);
        PlayerPrefs.Save();
    }
    
    #endregion
    
    #region Utilities
    
    public void NotifyAllListeners()
    {
        if (Time.time - lastNotificationTime < NOTIFICATION_THROTTLE_TIME)
        {
            return;
        }
        
        OnCurrencyChanged?.Invoke(_currentCurrency, _currentCurrency);
        lastNotificationTime = Time.time;
    }
    
    private void UpdateAllCurrencyDisplays()
    {
        if (Time.time - lastNotificationTime < NOTIFICATION_THROTTLE_TIME)
        {
            return;
        }

        // Update all ShopUI instances
        var shopUIs = FindObjectsOfType<ShopUI>();
        if (shopUIs != null && shopUIs.Length > 0)
        {
            foreach (var shopUI in shopUIs)
            {
                if (shopUI != null && shopUI.gameObject.activeInHierarchy)
                {
                    try
                    {
                        shopUI.UpdateCurrencyDisplay(_currentCurrency);
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"Error updating ShopUI currency display: {ex.Message}");
                    }
                }
            }
        }
        
        // Also update all currency labels directly to ensure proper formatting
        var moneyCounterAnimations = FindObjectsOfType<MoneyCounterAnimation>();
        if (moneyCounterAnimations != null && moneyCounterAnimations.Length > 0)
        {
            foreach (var moneyAnimation in moneyCounterAnimations)
            {
                if (moneyAnimation != null && moneyAnimation.gameObject.activeInHierarchy)
                {
                    // Find all UI documents and update their currency labels
                    var uiDocuments = FindObjectsOfType<UIDocument>();
                    foreach (var uiDoc in uiDocuments)
                    {
                        if (uiDoc != null && uiDoc.rootVisualElement != null)
                        {
                            var currencyLabels = uiDoc.rootVisualElement.Query<Label>().Where(l => 
                                l.text != null && 
                                l.text.Contains("€") && 
                                l.name != "BuyTotalValue" && 
                                l.name != "SellTotalValue" &&
                                !l.ClassListContains("shop-item-price")).ToList();
                            
                            foreach (var label in currencyLabels)
                            {
                                // Ensure proper formatting without animation
                                moneyAnimation.SetColoredCounterValue(label, _currentCurrency);
                            }
                        }
                    }
                }
            }
        }
    }
    
    #endregion
}
